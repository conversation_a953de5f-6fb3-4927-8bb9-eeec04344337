{"logs": [{"outputFile": "com.Aries.AliyunCertTool.app-mergeDebugResources-54:/values-ne/values-ne.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\cf96ea3900c1f15eea4117a61e73d29e\\transformed\\ui-release\\res\\values-ne\\values-ne.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "179,278,368,462,559,645,727,823,910,996,1086,1179,1256,1340,1415,1488,1560,1641,1709", "endColumns": "98,89,93,96,85,81,95,86,85,89,92,76,83,74,72,71,80,67,119", "endOffsets": "273,363,457,554,640,722,818,905,991,1081,1174,1251,1335,1410,1483,1555,1636,1704,1824"}, "to": {"startLines": "10,11,12,13,14,15,16,75,76,77,78,79,80,81,82,83,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "898,997,1087,1181,1278,1364,1446,8094,8181,8267,8357,8450,8527,8611,8686,8759,8932,9013,9081", "endColumns": "98,89,93,96,85,81,95,86,85,89,92,76,83,74,72,71,80,67,119", "endOffsets": "992,1082,1176,1273,1359,1441,1537,8176,8262,8352,8445,8522,8606,8681,8754,8826,9008,9076,9196"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b59206c725e3c03c5728e46c84e8a9c8\\transformed\\foundation-release\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,128,213", "endColumns": "72,84,90", "endOffsets": "123,208,299"}, "to": {"startLines": "2,88,89", "startColumns": "4,4,4", "startOffsets": "105,9201,9286", "endColumns": "72,84,90", "endOffsets": "173,9281,9372"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\70c31411d223bd8dbd73cf3117964047\\transformed\\core-1.13.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,261,363,469,567,667,775", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "153,256,358,464,562,662,770,871"}, "to": {"startLines": "3,4,5,6,7,8,9,84", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "178,281,384,486,592,690,790,8831", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "276,379,481,587,685,785,893,8927"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\71e603f77f5431c60c0bf20ec43c96c0\\transformed\\material-release\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "87", "endOffsets": "138"}, "to": {"startLines": "74", "startColumns": "4", "startOffsets": "8006", "endColumns": "87", "endOffsets": "8089"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9dddf8c9e00e2a90c0a65ed3f1ecb3d3\\transformed\\material3-release\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,184,303,419,547,646,741,853,1005,1126,1279,1363,1471,1569,1668,1780,1904,2017,2163,2306,2440,2605,2735,2887,3044,3173,3272,3367,3483,3607,3711,3830,3940,4086,4234,4344,4452,4527,4632,4737,4848,4939,5034,5141,5221,5306,5407,5516,5611,5714,5801,5912,6011,6116,6239,6319,6425", "endColumns": "128,118,115,127,98,94,111,151,120,152,83,107,97,98,111,123,112,145,142,133,164,129,151,156,128,98,94,115,123,103,118,109,145,147,109,107,74,104,104,110,90,94,106,79,84,100,108,94,102,86,110,98,104,122,79,105,93", "endOffsets": "179,298,414,542,641,736,848,1000,1121,1274,1358,1466,1564,1663,1775,1899,2012,2158,2301,2435,2600,2730,2882,3039,3168,3267,3362,3478,3602,3706,3825,3935,4081,4229,4339,4447,4522,4627,4732,4843,4934,5029,5136,5216,5301,5402,5511,5606,5709,5796,5907,6006,6111,6234,6314,6420,6514"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1542,1671,1790,1906,2034,2133,2228,2340,2492,2613,2766,2850,2958,3056,3155,3267,3391,3504,3650,3793,3927,4092,4222,4374,4531,4660,4759,4854,4970,5094,5198,5317,5427,5573,5721,5831,5939,6014,6119,6224,6335,6426,6521,6628,6708,6793,6894,7003,7098,7201,7288,7399,7498,7603,7726,7806,7912", "endColumns": "128,118,115,127,98,94,111,151,120,152,83,107,97,98,111,123,112,145,142,133,164,129,151,156,128,98,94,115,123,103,118,109,145,147,109,107,74,104,104,110,90,94,106,79,84,100,108,94,102,86,110,98,104,122,79,105,93", "endOffsets": "1666,1785,1901,2029,2128,2223,2335,2487,2608,2761,2845,2953,3051,3150,3262,3386,3499,3645,3788,3922,4087,4217,4369,4526,4655,4754,4849,4965,5089,5193,5312,5422,5568,5716,5826,5934,6009,6114,6219,6330,6421,6516,6623,6703,6788,6889,6998,7093,7196,7283,7394,7493,7598,7721,7801,7907,8001"}}]}]}