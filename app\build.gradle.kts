plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.jetbrains.kotlin.android)
}

android {
    namespace = "com.Aries.AliyunCertTool"
    compileSdk = 34

    defaultConfig {
        applicationId = "com.Aries.AliyunCertTool"
        minSdk = 26
        targetSdk = 34
        versionCode = 1
        versionName = "1.0"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary = true
        }
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = "1.8"
    }
    buildFeatures {
        compose = true
    }
    composeOptions {
        kotlinCompilerExtensionVersion = "1.5.4"
    }
    packaging {
        resources {
            excludes += "/META-INF/{AL2.0,LGPL2.1}"
        }
    }
}

dependencies {
    // ========== 阿里云 SDK ==========
    implementation(libs.aliyun.alb)  // 阿里云负载均衡服务 SDK
    implementation(libs.aliyun.cas)  // 阿里云证书服务 SDK

    // ========== Compose BOM (版本管理) ==========
    // BOM 确保所有 Compose 库使用兼容的版本，避免版本冲突
    implementation(platform(libs.androidx.compose.bom))
    androidTestImplementation(platform(libs.androidx.compose.bom))

    // ========== UI 设计系统 ==========
    // Material Design 3 - Google 最新的设计语言，包含按钮、卡片、导航等现代 UI 组件
    implementation(libs.androidx.material3)

    // Material Design 2 - 上一代设计语言 (可选，通常选择其中一个)
    // implementation(libs.androidx.compose.material)

    // Foundation - Compose 基础组件库，用于构建自定义 UI 组件
    // 包含：布局、手势、绘制、动画等底层功能
    implementation(libs.androidx.compose.foundation)

    // ========== 开发工具 ==========
    // UI 预览支持 - 在 Android Studio 中实时预览 Compose UI
    implementation(libs.androidx.ui.tooling.preview)
    // UI 调试工具 - 仅在调试版本中包含，用于检查 UI 层次结构
    debugImplementation(libs.androidx.ui.tooling)

    // ========== 测试工具 ==========
    // UI 自动化测试 - 用于编写和运行 Compose UI 测试
    androidTestImplementation(libs.androidx.ui.test.junit4)
    // 测试清单 - 仅在调试版本中包含，支持 UI 测试
    debugImplementation(libs.androidx.ui.test.manifest)

    // ========== 图标库 ==========
    // Material 图标核心库 - 包含常用的基础图标 (约 1000+ 个)
    implementation(libs.androidx.material.icons.core)
    // Material 图标扩展库 - 包含完整的图标集 (约 5000+ 个)
    implementation(libs.androidx.material.icons)

    // ========== 响应式设计 ==========
    // Material3 自适应组件 - 帮助应用适配不同屏幕尺寸 (手机、平板、折叠屏)
    implementation(libs.androidx.material3.adaptive)


    // ========== Android 核心库 ==========
    // Android KTX 扩展 - 提供 Kotlin 友好的 Android API 扩展函数
    implementation(libs.androidx.core.ktx)
    // 生命周期运行时 - 管理 Activity/Fragment 的生命周期
    implementation(libs.androidx.lifecycle.runtime.ktx)

    // ========== Compose 核心组件 ==========
    // Activity 与 Compose 集成 - 让 Activity 可以使用 Compose UI
    implementation(libs.androidx.activity.compose)
    // Compose UI 核心 - 基础 UI 组件 (Text, Button, Column, Row 等)
    implementation(libs.androidx.ui)
    // UI 图形库 - 处理颜色、画笔、路径等图形相关功能
    implementation(libs.androidx.ui.graphics)

    // ========== 架构组件集成 ==========
    // ViewModel 与 Compose 集成 - 在 Compose 中使用 ViewModel 管理 UI 状态
    // 提供 viewModel() 函数和状态管理功能
    implementation(libs.androidx.lifecycle.viewmodel.compose)

    // LiveData 与 Compose 集成 - 将 LiveData 转换为 Compose State
    // 使用 observeAsState() 函数观察 LiveData 变化
    implementation(libs.androidx.compose.runtime.livedata)

    // RxJava 与 Compose 集成 (可选) - 将 RxJava Observable 转换为 Compose State
    // 如果项目使用 RxJava 进行响应式编程，可以启用此依赖
    // implementation(libs.androidx.compose.runtime.rxjava2)


    // ========== 测试框架 ==========
    // JUnit - Java/Kotlin 单元测试框架，用于测试业务逻辑
    testImplementation(libs.junit)
    // AndroidX JUnit - Android 平台的 JUnit 扩展，用于 Android 相关测试
    androidTestImplementation(libs.androidx.junit)
    // Espresso - Android UI 自动化测试框架，用于测试用户界面交互
    androidTestImplementation(libs.androidx.espresso.core)



}