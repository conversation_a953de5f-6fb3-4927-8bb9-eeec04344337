1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.Aries.AliyunCertTool"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="34" />
10
11    <permission
11-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
12        android:name="com.Aries.AliyunCertTool.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="com.Aries.AliyunCertTool.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
16
17    <application
17-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:5:5-26:19
18        android:allowBackup="true"
18-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:6:9-35
19        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
19-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
20        android:dataExtractionRules="@xml/data_extraction_rules"
20-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:7:9-65
21        android:debuggable="true"
22        android:extractNativeLibs="false"
23        android:fullBackupContent="@xml/backup_rules"
23-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:8:9-54
24        android:icon="@mipmap/ic_launcher"
24-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:9:9-43
25        android:label="@string/app_name"
25-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:10:9-41
26        android:roundIcon="@mipmap/ic_launcher_round"
26-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:11:9-54
27        android:supportsRtl="true"
27-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:12:9-35
28        android:theme="@style/Theme.AliyunCertTool" >
28-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:13:9-52
29        <activity
29-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:15:9-25:20
30            android:name="com.Aries.AliyunCertTool.MainActivity"
30-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:16:13-41
31            android:exported="true"
31-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:17:13-36
32            android:label="@string/app_name"
32-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:18:13-45
33            android:theme="@style/Theme.AliyunCertTool" >
33-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:19:13-56
34            <intent-filter>
34-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:20:13-24:29
35                <action android:name="android.intent.action.MAIN" />
35-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:21:17-69
35-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:21:25-66
36
37                <category android:name="android.intent.category.LAUNCHER" />
37-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:23:17-77
37-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:23:27-74
38            </intent-filter>
39        </activity>
40        <activity
40-->[androidx.compose.ui:ui-tooling-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\fa6194f87e2a819412e48af269513aee\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
41            android:name="androidx.compose.ui.tooling.PreviewActivity"
41-->[androidx.compose.ui:ui-tooling-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\fa6194f87e2a819412e48af269513aee\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
42            android:exported="true" />
42-->[androidx.compose.ui:ui-tooling-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\fa6194f87e2a819412e48af269513aee\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
43        <activity
43-->[androidx.compose.ui:ui-test-manifest:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\b0843a9727e1c6b00d1ab50239f395df\transformed\ui-test-manifest-1.8.1\AndroidManifest.xml:23:9-26:79
44            android:name="androidx.activity.ComponentActivity"
44-->[androidx.compose.ui:ui-test-manifest:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\b0843a9727e1c6b00d1ab50239f395df\transformed\ui-test-manifest-1.8.1\AndroidManifest.xml:24:13-63
45            android:exported="true"
45-->[androidx.compose.ui:ui-test-manifest:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\b0843a9727e1c6b00d1ab50239f395df\transformed\ui-test-manifest-1.8.1\AndroidManifest.xml:25:13-36
46            android:theme="@android:style/Theme.Material.Light.NoActionBar" />
46-->[androidx.compose.ui:ui-test-manifest:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\b0843a9727e1c6b00d1ab50239f395df\transformed\ui-test-manifest-1.8.1\AndroidManifest.xml:26:13-76
47
48        <uses-library
48-->[androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b5ca4484bc6448589137ef4a4606beed\transformed\window-1.3.0\AndroidManifest.xml:23:9-25:40
49            android:name="androidx.window.extensions"
49-->[androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b5ca4484bc6448589137ef4a4606beed\transformed\window-1.3.0\AndroidManifest.xml:24:13-54
50            android:required="false" />
50-->[androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b5ca4484bc6448589137ef4a4606beed\transformed\window-1.3.0\AndroidManifest.xml:25:13-37
51        <uses-library
51-->[androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b5ca4484bc6448589137ef4a4606beed\transformed\window-1.3.0\AndroidManifest.xml:26:9-28:40
52            android:name="androidx.window.sidecar"
52-->[androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b5ca4484bc6448589137ef4a4606beed\transformed\window-1.3.0\AndroidManifest.xml:27:13-51
53            android:required="false" />
53-->[androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b5ca4484bc6448589137ef4a4606beed\transformed\window-1.3.0\AndroidManifest.xml:28:13-37
54
55        <provider
55-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\be21046955a2cf2f08dc611156219bcb\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
56            android:name="androidx.startup.InitializationProvider"
56-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\be21046955a2cf2f08dc611156219bcb\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
57            android:authorities="com.Aries.AliyunCertTool.androidx-startup"
57-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\be21046955a2cf2f08dc611156219bcb\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
58            android:exported="false" >
58-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\be21046955a2cf2f08dc611156219bcb\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
59            <meta-data
59-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\be21046955a2cf2f08dc611156219bcb\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
60                android:name="androidx.emoji2.text.EmojiCompatInitializer"
60-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\be21046955a2cf2f08dc611156219bcb\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
61                android:value="androidx.startup" />
61-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\be21046955a2cf2f08dc611156219bcb\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
62            <meta-data
62-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\b7a645b4501d9bd8a893093c15e3c8d8\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
63                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
63-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\b7a645b4501d9bd8a893093c15e3c8d8\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
64                android:value="androidx.startup" />
64-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\b7a645b4501d9bd8a893093c15e3c8d8\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
65            <meta-data
65-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
66                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
66-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
67                android:value="androidx.startup" />
67-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
68        </provider>
69
70        <receiver
70-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
71            android:name="androidx.profileinstaller.ProfileInstallReceiver"
71-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
72            android:directBootAware="false"
72-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
73            android:enabled="true"
73-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
74            android:exported="true"
74-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
75            android:permission="android.permission.DUMP" >
75-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
76            <intent-filter>
76-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
77                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
77-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
77-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
78            </intent-filter>
79            <intent-filter>
79-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
80                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
80-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
80-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
81            </intent-filter>
82            <intent-filter>
82-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
83                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
83-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
83-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
84            </intent-filter>
85            <intent-filter>
85-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
86                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
86-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
86-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
87            </intent-filter>
88        </receiver>
89    </application>
90
91</manifest>
