1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.Aries.AliyunCertTool"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="34" />
10
11    <permission
11-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
12        android:name="com.Aries.AliyunCertTool.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="com.Aries.AliyunCertTool.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
16
17    <application
17-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:5:5-26:19
18        android:allowBackup="true"
18-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:6:9-35
19        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
19-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
20        android:dataExtractionRules="@xml/data_extraction_rules"
20-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:7:9-65
21        android:debuggable="true"
22        android:extractNativeLibs="false"
23        android:fullBackupContent="@xml/backup_rules"
23-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:8:9-54
24        android:icon="@mipmap/ic_launcher"
24-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:9:9-43
25        android:label="@string/app_name"
25-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:10:9-41
26        android:roundIcon="@mipmap/ic_launcher_round"
26-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:11:9-54
27        android:supportsRtl="true"
27-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:12:9-35
28        android:theme="@style/Theme.AliyunCertTool" >
28-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:13:9-52
29        <activity
29-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:15:9-25:20
30            android:name="com.Aries.AliyunCertTool.MainActivity"
30-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:16:13-41
31            android:exported="true"
31-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:17:13-36
32            android:label="@string/app_name"
32-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:18:13-45
33            android:theme="@style/Theme.AliyunCertTool" >
33-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:19:13-56
34            <intent-filter>
34-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:20:13-24:29
35                <action android:name="android.intent.action.MAIN" />
35-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:21:17-69
35-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:21:25-66
36
37                <category android:name="android.intent.category.LAUNCHER" />
37-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:23:17-77
37-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:23:27-74
38            </intent-filter>
39        </activity>
40        <activity
40-->[androidx.compose.ui:ui-tooling-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\75f659e769d841329b7f2d2243cabfd5\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
41            android:name="androidx.compose.ui.tooling.PreviewActivity"
41-->[androidx.compose.ui:ui-tooling-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\75f659e769d841329b7f2d2243cabfd5\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
42            android:exported="true" />
42-->[androidx.compose.ui:ui-tooling-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\75f659e769d841329b7f2d2243cabfd5\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
43        <activity
43-->[androidx.compose.ui:ui-test-manifest:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\b1e63eb5eb0681e2e793bedfd5b2b54c\transformed\ui-test-manifest-1.7.3\AndroidManifest.xml:23:9-25:39
44            android:name="androidx.activity.ComponentActivity"
44-->[androidx.compose.ui:ui-test-manifest:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\b1e63eb5eb0681e2e793bedfd5b2b54c\transformed\ui-test-manifest-1.7.3\AndroidManifest.xml:24:13-63
45            android:exported="true" />
45-->[androidx.compose.ui:ui-test-manifest:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\b1e63eb5eb0681e2e793bedfd5b2b54c\transformed\ui-test-manifest-1.7.3\AndroidManifest.xml:25:13-36
46
47        <provider
47-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cdf3e867b1382a5d5b2e8e0e042d4c1c\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
48            android:name="androidx.startup.InitializationProvider"
48-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cdf3e867b1382a5d5b2e8e0e042d4c1c\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
49            android:authorities="com.Aries.AliyunCertTool.androidx-startup"
49-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cdf3e867b1382a5d5b2e8e0e042d4c1c\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
50            android:exported="false" >
50-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cdf3e867b1382a5d5b2e8e0e042d4c1c\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
51            <meta-data
51-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cdf3e867b1382a5d5b2e8e0e042d4c1c\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
52                android:name="androidx.emoji2.text.EmojiCompatInitializer"
52-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cdf3e867b1382a5d5b2e8e0e042d4c1c\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
53                android:value="androidx.startup" />
53-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cdf3e867b1382a5d5b2e8e0e042d4c1c\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
54            <meta-data
54-->[androidx.lifecycle:lifecycle-process:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\8b095931678b4e60ac63c99c8bf47483\transformed\lifecycle-process-2.8.5\AndroidManifest.xml:29:13-31:52
55                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
55-->[androidx.lifecycle:lifecycle-process:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\8b095931678b4e60ac63c99c8bf47483\transformed\lifecycle-process-2.8.5\AndroidManifest.xml:30:17-78
56                android:value="androidx.startup" />
56-->[androidx.lifecycle:lifecycle-process:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\8b095931678b4e60ac63c99c8bf47483\transformed\lifecycle-process-2.8.5\AndroidManifest.xml:31:17-49
57            <meta-data
57-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
58                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
58-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
59                android:value="androidx.startup" />
59-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
60        </provider>
61
62        <uses-library
62-->[androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b5ca4484bc6448589137ef4a4606beed\transformed\window-1.3.0\AndroidManifest.xml:23:9-25:40
63            android:name="androidx.window.extensions"
63-->[androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b5ca4484bc6448589137ef4a4606beed\transformed\window-1.3.0\AndroidManifest.xml:24:13-54
64            android:required="false" />
64-->[androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b5ca4484bc6448589137ef4a4606beed\transformed\window-1.3.0\AndroidManifest.xml:25:13-37
65        <uses-library
65-->[androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b5ca4484bc6448589137ef4a4606beed\transformed\window-1.3.0\AndroidManifest.xml:26:9-28:40
66            android:name="androidx.window.sidecar"
66-->[androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b5ca4484bc6448589137ef4a4606beed\transformed\window-1.3.0\AndroidManifest.xml:27:13-51
67            android:required="false" />
67-->[androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b5ca4484bc6448589137ef4a4606beed\transformed\window-1.3.0\AndroidManifest.xml:28:13-37
68
69        <receiver
69-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
70            android:name="androidx.profileinstaller.ProfileInstallReceiver"
70-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
71            android:directBootAware="false"
71-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
72            android:enabled="true"
72-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
73            android:exported="true"
73-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
74            android:permission="android.permission.DUMP" >
74-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
75            <intent-filter>
75-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
76                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
76-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
76-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
77            </intent-filter>
78            <intent-filter>
78-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
79                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
79-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
79-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
80            </intent-filter>
81            <intent-filter>
81-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
82                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
82-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
82-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
83            </intent-filter>
84            <intent-filter>
84-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
85                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
85-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
85-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
86            </intent-filter>
87        </receiver>
88    </application>
89
90</manifest>
