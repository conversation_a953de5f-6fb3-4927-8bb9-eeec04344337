1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.Aries.AliyunCertTool"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="34" />
10
11    <permission
11-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
12        android:name="com.Aries.AliyunCertTool.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="com.Aries.AliyunCertTool.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
16
17    <application
17-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:5:5-26:19
18        android:allowBackup="true"
18-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:6:9-35
19        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
19-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
20        android:dataExtractionRules="@xml/data_extraction_rules"
20-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:7:9-65
21        android:debuggable="true"
22        android:extractNativeLibs="false"
23        android:fullBackupContent="@xml/backup_rules"
23-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:8:9-54
24        android:icon="@mipmap/ic_launcher"
24-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:9:9-43
25        android:label="@string/app_name"
25-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:10:9-41
26        android:roundIcon="@mipmap/ic_launcher_round"
26-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:11:9-54
27        android:supportsRtl="true"
27-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:12:9-35
28        android:testOnly="true"
29        android:theme="@style/Theme.AliyunCertTool" >
29-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:13:9-52
30        <activity
30-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:15:9-25:20
31            android:name="com.Aries.AliyunCertTool.MainActivity"
31-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:16:13-41
32            android:exported="true"
32-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:17:13-36
33            android:label="@string/app_name"
33-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:18:13-45
34            android:theme="@style/Theme.AliyunCertTool" >
34-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:19:13-56
35            <intent-filter>
35-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:20:13-24:29
36                <action android:name="android.intent.action.MAIN" />
36-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:21:17-69
36-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:21:25-66
37
38                <category android:name="android.intent.category.LAUNCHER" />
38-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:23:17-77
38-->D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:23:27-74
39            </intent-filter>
40        </activity>
41        <activity
41-->[androidx.compose.ui:ui-tooling-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\75f659e769d841329b7f2d2243cabfd5\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
42            android:name="androidx.compose.ui.tooling.PreviewActivity"
42-->[androidx.compose.ui:ui-tooling-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\75f659e769d841329b7f2d2243cabfd5\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
43            android:exported="true" />
43-->[androidx.compose.ui:ui-tooling-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\75f659e769d841329b7f2d2243cabfd5\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
44        <activity
44-->[androidx.compose.ui:ui-test-manifest:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\b1e63eb5eb0681e2e793bedfd5b2b54c\transformed\ui-test-manifest-1.7.3\AndroidManifest.xml:23:9-25:39
45            android:name="androidx.activity.ComponentActivity"
45-->[androidx.compose.ui:ui-test-manifest:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\b1e63eb5eb0681e2e793bedfd5b2b54c\transformed\ui-test-manifest-1.7.3\AndroidManifest.xml:24:13-63
46            android:exported="true" />
46-->[androidx.compose.ui:ui-test-manifest:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\b1e63eb5eb0681e2e793bedfd5b2b54c\transformed\ui-test-manifest-1.7.3\AndroidManifest.xml:25:13-36
47
48        <provider
48-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cdf3e867b1382a5d5b2e8e0e042d4c1c\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
49            android:name="androidx.startup.InitializationProvider"
49-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cdf3e867b1382a5d5b2e8e0e042d4c1c\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
50            android:authorities="com.Aries.AliyunCertTool.androidx-startup"
50-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cdf3e867b1382a5d5b2e8e0e042d4c1c\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
51            android:exported="false" >
51-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cdf3e867b1382a5d5b2e8e0e042d4c1c\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
52            <meta-data
52-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cdf3e867b1382a5d5b2e8e0e042d4c1c\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
53                android:name="androidx.emoji2.text.EmojiCompatInitializer"
53-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cdf3e867b1382a5d5b2e8e0e042d4c1c\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
54                android:value="androidx.startup" />
54-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cdf3e867b1382a5d5b2e8e0e042d4c1c\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
55            <meta-data
55-->[androidx.lifecycle:lifecycle-process:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\8b095931678b4e60ac63c99c8bf47483\transformed\lifecycle-process-2.8.5\AndroidManifest.xml:29:13-31:52
56                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
56-->[androidx.lifecycle:lifecycle-process:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\8b095931678b4e60ac63c99c8bf47483\transformed\lifecycle-process-2.8.5\AndroidManifest.xml:30:17-78
57                android:value="androidx.startup" />
57-->[androidx.lifecycle:lifecycle-process:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\8b095931678b4e60ac63c99c8bf47483\transformed\lifecycle-process-2.8.5\AndroidManifest.xml:31:17-49
58            <meta-data
58-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
59                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
59-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
60                android:value="androidx.startup" />
60-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
61        </provider>
62
63        <uses-library
63-->[androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b5ca4484bc6448589137ef4a4606beed\transformed\window-1.3.0\AndroidManifest.xml:23:9-25:40
64            android:name="androidx.window.extensions"
64-->[androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b5ca4484bc6448589137ef4a4606beed\transformed\window-1.3.0\AndroidManifest.xml:24:13-54
65            android:required="false" />
65-->[androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b5ca4484bc6448589137ef4a4606beed\transformed\window-1.3.0\AndroidManifest.xml:25:13-37
66        <uses-library
66-->[androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b5ca4484bc6448589137ef4a4606beed\transformed\window-1.3.0\AndroidManifest.xml:26:9-28:40
67            android:name="androidx.window.sidecar"
67-->[androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b5ca4484bc6448589137ef4a4606beed\transformed\window-1.3.0\AndroidManifest.xml:27:13-51
68            android:required="false" />
68-->[androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b5ca4484bc6448589137ef4a4606beed\transformed\window-1.3.0\AndroidManifest.xml:28:13-37
69
70        <receiver
70-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
71            android:name="androidx.profileinstaller.ProfileInstallReceiver"
71-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
72            android:directBootAware="false"
72-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
73            android:enabled="true"
73-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
74            android:exported="true"
74-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
75            android:permission="android.permission.DUMP" >
75-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
76            <intent-filter>
76-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
77                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
77-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
77-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
78            </intent-filter>
79            <intent-filter>
79-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
80                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
80-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
80-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
81            </intent-filter>
82            <intent-filter>
82-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
83                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
83-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
83-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
84            </intent-filter>
85            <intent-filter>
85-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
86                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
86-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
86-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
87            </intent-filter>
88        </receiver>
89    </application>
90
91</manifest>
