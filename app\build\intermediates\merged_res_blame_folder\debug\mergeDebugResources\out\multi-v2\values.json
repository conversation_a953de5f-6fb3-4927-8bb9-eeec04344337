{"logs": [{"outputFile": "com.Aries.AliyunCertTool.app-mergeDebugResources-53:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\e5c03f6b4f5fe33a166d0205ccd1d4ac\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "158,176,177,178,179,180,181,182,183,184,185,188,189,190,191,192,193,194,195,196,197,198,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,254,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7916,9161,9249,9335,9416,9500,9569,9634,9717,9823,9909,10029,10083,10152,10213,10282,10371,10466,10540,10637,10730,10828,10977,11068,11156,11252,11350,11414,11482,11569,11663,11730,11802,11874,11975,12084,12160,12229,12277,12343,12407,12481,12538,12595,12667,12717,12771,12842,12913,12983,13052,13110,13186,13257,13331,13417,13467,13537,14463,15178", "endLines": "158,176,177,178,179,180,181,182,183,184,187,188,189,190,191,192,193,194,195,196,197,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,263,266", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "7984,9244,9330,9411,9495,9564,9629,9712,9818,9904,10024,10078,10147,10208,10277,10366,10461,10535,10632,10725,10823,10972,11063,11151,11247,11345,11409,11477,11564,11658,11725,11797,11869,11970,12079,12155,12224,12272,12338,12402,12476,12533,12590,12662,12712,12766,12837,12908,12978,13047,13105,13181,13252,13326,13412,13462,13532,13597,15173,15326"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\70c31411d223bd8dbd73cf3117964047\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "25,55,56,58,59,60,61,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,133,134,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,159,162,163,164,165,166,167,168,245,274,275,279,280,284,286,287,299,305,315,348,369,402", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "820,1818,1890,2020,2085,2151,2220,2556,2626,2694,2766,2836,2897,2971,3044,3105,3166,3228,3292,3354,3415,3483,3583,3643,3709,3782,3851,3908,3960,4022,4094,4170,4235,4294,4353,4413,4473,4533,4593,4653,4713,4773,4833,4893,4953,5012,5072,5132,5192,5252,5312,5372,5432,5492,5552,5612,5671,5731,5791,5850,5909,5968,6027,6086,6618,6653,6797,6852,6915,6970,7028,7086,7147,7210,7267,7318,7368,7429,7486,7552,7586,7621,7989,8194,8261,8333,8402,8471,8545,8617,13996,15637,15754,15955,16065,16266,16486,16558,16978,17181,17482,19213,19894,20576", "endLines": "25,55,56,58,59,60,61,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,133,134,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,159,162,163,164,165,166,167,168,245,274,278,279,283,284,286,287,304,314,347,368,401,407", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "875,1885,1973,2080,2146,2215,2278,2621,2689,2761,2831,2892,2966,3039,3100,3161,3223,3287,3349,3410,3478,3578,3638,3704,3777,3846,3903,3955,4017,4089,4165,4230,4289,4348,4408,4468,4528,4588,4648,4708,4768,4828,4888,4948,5007,5067,5127,5187,5247,5307,5367,5427,5487,5547,5607,5666,5726,5786,5845,5904,5963,6022,6081,6140,6648,6683,6847,6910,6965,7023,7081,7142,7205,7262,7313,7363,7424,7481,7547,7581,7616,7651,8054,8256,8328,8397,8466,8540,8612,8700,14062,15749,15950,16060,16261,16390,16553,16620,17176,17477,19208,19889,20571,20738"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b3a49040c74edc5e4177638a7355a843\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "153", "startColumns": "4", "startOffsets": "7656", "endColumns": "42", "endOffsets": "7694"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\7d3a4f80790e465d9b992b6fa510745d\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "156", "startColumns": "4", "startOffsets": "7813", "endColumns": "49", "endOffsets": "7858"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8eb842c10b8c11b900558b9b1d222889\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "160", "startColumns": "4", "startOffsets": "8059", "endColumns": "82", "endOffsets": "8137"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a6ebdcd9fe3662fa7e0f8e75496e5f16\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "132,135", "startColumns": "4,4", "startOffsets": "6564,6688", "endColumns": "53,66", "endOffsets": "6613,6750"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\e810deac697feab8d2a0acfdc8f9cef5\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,61,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3028,3082,3128,3174,3216,3256,3303,3339,3429,3541,3652", "endLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,67", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3023,3077,3123,3169,3211,3251,3298,3334,3424,3536,3647,3842"}, "to": {"startLines": "125,127,128,130,131,157,169,170,171,172,173,174,175,237,238,239,240,241,242,243,244,246,247,248,251,267,270", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6145,6279,6337,6458,6509,7863,8705,8770,8824,8890,8991,9049,9101,13602,13664,13718,13768,13822,13868,13914,13956,14067,14114,14150,14351,15331,15442", "endLines": "125,127,128,130,131,157,169,170,171,172,173,174,175,237,238,239,240,241,242,243,244,246,247,248,253,269,273", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "6214,6332,6387,6504,6559,7911,8765,8819,8885,8986,9044,9096,9156,13659,13713,13763,13817,13863,13909,13951,13991,14109,14145,14235,14458,15437,15632"}}, {"source": "D:\\AndroidProject\\AliyunCertTool\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "56", "endColumns": "91", "endOffsets": "143"}, "to": {"startLines": "285", "startColumns": "4", "startOffsets": "16395", "endColumns": "90", "endOffsets": "16481"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b5ca4484bc6448589137ef4a4606beed\\transformed\\window-1.3.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "2,3,9,17,26,38,44,50,51,52,53,54,126,288,294,408,416,431", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,382,601,880,1194,1382,1569,1622,1682,1734,1779,6219,16625,16820,20743,21025,21639", "endLines": "2,8,16,24,37,43,49,50,51,52,53,54,126,293,298,415,430,446", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "204,377,596,815,1189,1377,1564,1617,1677,1729,1774,1813,6274,16815,16973,21020,21634,22288"}}, {"source": "D:\\AndroidProject\\AliyunCertTool\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "16", "endColumns": "51", "endOffsets": "63"}, "to": {"startLines": "161", "startColumns": "4", "startOffsets": "8142", "endColumns": "51", "endOffsets": "8189"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a51497edcabbd2502676cd98d1603be0\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "249,250", "startColumns": "4,4", "startOffsets": "14240,14296", "endColumns": "55,54", "endOffsets": "14291,14346"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\d294e7ab832ca6f45f919801eb74ab9b\\transformed\\activity-1.9.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "136,154", "startColumns": "4,4", "startOffsets": "6755,7699", "endColumns": "41,59", "endOffsets": "6792,7754"}}, {"source": "D:\\AndroidProject\\AliyunCertTool\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "286,55,102,149,196,241,328", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "323,97,144,191,236,281,365"}, "to": {"startLines": "57,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "1978,2283,2330,2377,2424,2469,2514", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "2015,2325,2372,2419,2464,2509,2551"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\89cde597d5165d323f3b1e7d17e26cc4\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "129", "startColumns": "4", "startOffsets": "6392", "endColumns": "65", "endOffsets": "6453"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a56d10200708944b8fc73cc8e55f3ae2\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "155", "startColumns": "4", "startOffsets": "7759", "endColumns": "53", "endOffsets": "7808"}}]}]}