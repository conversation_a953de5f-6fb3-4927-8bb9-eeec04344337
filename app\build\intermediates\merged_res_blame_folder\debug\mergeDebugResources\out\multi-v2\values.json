{"logs": [{"outputFile": "com.Aries.AliyunCertTool.app-mergeDebugResources-54:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\70c31411d223bd8dbd73cf3117964047\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "25,55,56,58,59,60,61,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,133,134,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,160,164,165,166,167,168,169,170,249,279,280,284,285,289,291,292,304,310,320,353,374,407", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "820,1818,1890,2020,2085,2151,2220,2556,2626,2694,2766,2836,2897,2971,3044,3105,3166,3228,3292,3354,3415,3483,3583,3643,3709,3782,3851,3908,3960,4022,4094,4170,4235,4294,4353,4413,4473,4533,4593,4653,4713,4773,4833,4893,4953,5012,5072,5132,5192,5252,5312,5372,5432,5492,5552,5612,5671,5731,5791,5850,5909,5968,6027,6086,6618,6653,6797,6852,6915,6970,7028,7086,7147,7210,7267,7318,7368,7429,7486,7552,7586,7621,8032,8283,8350,8422,8491,8560,8634,8706,14197,15900,16017,16218,16328,16529,16749,16821,17241,17444,17745,19476,20157,20839", "endLines": "25,55,56,58,59,60,61,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,133,134,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,160,164,165,166,167,168,169,170,249,279,283,284,288,289,291,292,309,319,352,373,406,412", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "875,1885,1973,2080,2146,2215,2278,2621,2689,2761,2831,2892,2966,3039,3100,3161,3223,3287,3349,3410,3478,3578,3638,3704,3777,3846,3903,3955,4017,4089,4165,4230,4289,4348,4408,4468,4528,4588,4648,4708,4768,4828,4888,4948,5007,5067,5127,5187,5247,5307,5367,5427,5487,5547,5607,5666,5726,5786,5845,5904,5963,6022,6081,6140,6648,6683,6847,6910,6965,7023,7081,7142,7205,7262,7313,7363,7424,7481,7547,7581,7616,7651,8097,8345,8417,8486,8555,8629,8701,8789,14263,16012,16213,16323,16524,16653,16816,16883,17439,17740,19471,20152,20834,21001"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8eb842c10b8c11b900558b9b1d222889\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "161", "startColumns": "4", "startOffsets": "8102", "endColumns": "82", "endOffsets": "8180"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a6ebdcd9fe3662fa7e0f8e75496e5f16\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "132,135", "startColumns": "4,4", "startOffsets": "6564,6688", "endColumns": "53,66", "endOffsets": "6613,6750"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f1ab4ef3899c4192b0e0334f75282d3a\\transformed\\activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "136,155", "startColumns": "4,4", "startOffsets": "6755,7742", "endColumns": "41,59", "endOffsets": "6792,7797"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\99ec7351f88673f7e3413c630d55da71\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "154", "startColumns": "4", "startOffsets": "7699", "endColumns": "42", "endOffsets": "7737"}}, {"source": "D:\\AndroidProject\\AliyunCertTool\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "56", "endColumns": "91", "endOffsets": "143"}, "to": {"startLines": "290", "startColumns": "4", "startOffsets": "16658", "endColumns": "90", "endOffsets": "16744"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b5ca4484bc6448589137ef4a4606beed\\transformed\\window-1.3.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "2,3,9,17,26,38,44,50,51,52,53,54,126,293,299,413,421,436", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,382,601,880,1194,1382,1569,1622,1682,1734,1779,6219,16888,17083,21006,21288,21902", "endLines": "2,8,16,24,37,43,49,50,51,52,53,54,126,298,303,420,435,451", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "204,377,596,815,1189,1377,1564,1617,1677,1729,1774,1813,6274,17078,17236,21283,21897,22551"}}, {"source": "D:\\AndroidProject\\AliyunCertTool\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "16", "endColumns": "51", "endOffsets": "63"}, "to": {"startLines": "162", "startColumns": "4", "startOffsets": "8185", "endColumns": "51", "endOffsets": "8232"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\71e603f77f5431c60c0bf20ec43c96c0\\transformed\\material-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "57", "endOffsets": "108"}, "to": {"startLines": "239", "startColumns": "4", "startOffsets": "13691", "endColumns": "57", "endOffsets": "13744"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9dddf8c9e00e2a90c0a65ed3f1ecb3d3\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "159,178,179,180,181,182,183,184,185,186,187,190,191,192,193,194,195,196,197,198,199,200,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,258,268", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7959,9250,9338,9424,9505,9589,9658,9723,9806,9912,9998,10118,10172,10241,10302,10371,10460,10555,10629,10726,10819,10917,11066,11157,11245,11341,11439,11503,11571,11658,11752,11819,11891,11963,12064,12173,12249,12318,12366,12432,12496,12570,12627,12684,12756,12806,12860,12931,13002,13072,13141,13199,13275,13346,13420,13506,13556,13626,14664,15379", "endLines": "159,178,179,180,181,182,183,184,185,186,189,190,191,192,193,194,195,196,197,198,199,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,267,270", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "8027,9333,9419,9500,9584,9653,9718,9801,9907,9993,10113,10167,10236,10297,10366,10455,10550,10624,10721,10814,10912,11061,11152,11240,11336,11434,11498,11566,11653,11747,11814,11886,11958,12059,12168,12244,12313,12361,12427,12491,12565,12622,12679,12751,12801,12855,12926,12997,13067,13136,13194,13270,13341,13415,13501,13551,13621,13686,15374,15527"}}, {"source": "D:\\AndroidProject\\AliyunCertTool\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "286,55,102,149,196,241,328", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "323,97,144,191,236,281,365"}, "to": {"startLines": "57,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "1978,2283,2330,2377,2424,2469,2514", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "2015,2325,2372,2419,2464,2509,2551"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a435ffc347de873fb9a97b1b06faac5f\\transformed\\core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "153", "startColumns": "4", "startOffsets": "7656", "endColumns": "42", "endOffsets": "7694"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\1bb331d492c019dcf26e1da7719e21ab\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "129", "startColumns": "4", "startOffsets": "6392", "endColumns": "65", "endOffsets": "6453"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b59206c725e3c03c5728e46c84e8a9c8\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,101,157", "endColumns": "45,55,54", "endOffsets": "96,152,207"}, "to": {"startLines": "163,253,254", "startColumns": "4,4,4", "startOffsets": "8237,14441,14497", "endColumns": "45,55,54", "endOffsets": "8278,14492,14547"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\cf96ea3900c1f15eea4117a61e73d29e\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "34,35,36,37,38,39,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,63,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2060,2134,2192,2247,2298,2353,2452,2517,2571,2637,2738,2796,2848,2908,2970,3024,3074,3128,3174,3228,3274,3316,3356,3403,3439,3529,3641,3752", "endLines": "34,35,36,37,38,39,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,62,65,70", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,53,45,41,39,46,35,89,12,12,12", "endOffsets": "2129,2187,2242,2293,2348,2401,2512,2566,2632,2733,2791,2843,2903,2965,3019,3069,3123,3169,3223,3269,3311,3351,3398,3434,3524,3636,3747,4004"}, "to": {"startLines": "125,127,128,130,131,158,171,172,173,174,175,176,177,240,241,242,243,244,245,246,247,248,250,251,252,255,271,274", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6145,6279,6337,6458,6509,7906,8794,8859,8913,8979,9080,9138,9190,13749,13811,13865,13915,13969,14015,14069,14115,14157,14268,14315,14351,14552,15532,15643", "endLines": "125,127,128,130,131,158,171,172,173,174,175,176,177,240,241,242,243,244,245,246,247,248,250,251,252,257,273,278", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,53,45,41,39,46,35,89,12,12,12", "endOffsets": "6214,6332,6387,6504,6559,7954,8854,8908,8974,9075,9133,9185,9245,13806,13860,13910,13964,14010,14064,14110,14152,14192,14310,14346,14436,14659,15638,15895"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a56d10200708944b8fc73cc8e55f3ae2\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "156", "startColumns": "4", "startOffsets": "7802", "endColumns": "53", "endOffsets": "7851"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\0de59ce3c0beaf628d0c1f596e27f0c0\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "157", "startColumns": "4", "startOffsets": "7856", "endColumns": "49", "endOffsets": "7901"}}]}]}