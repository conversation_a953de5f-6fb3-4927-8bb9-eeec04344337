{"logs": [{"outputFile": "com.Aries.AliyunCertTool.app-mergeDebugResources-54:/values-ar/values-ar.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\70c31411d223bd8dbd73cf3117964047\\transformed\\core-1.13.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,250,345,448,551,653,767", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "143,245,340,443,546,648,762,863"}, "to": {"startLines": "3,4,5,6,7,8,9,84", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "186,279,381,476,579,682,784,8600", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "274,376,471,574,677,779,893,8696"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\71e603f77f5431c60c0bf20ec43c96c0\\transformed\\material-release\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "87", "endOffsets": "138"}, "to": {"startLines": "74", "startColumns": "4", "startOffsets": "7797", "endColumns": "87", "endOffsets": "7880"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\cf96ea3900c1f15eea4117a61e73d29e\\transformed\\ui-release\\res\\values-ar\\values-ar.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "187,276,359,454,552,637,718,824,908,989,1070,1153,1223,1307,1386,1465,1539,1615,1689", "endColumns": "88,82,94,97,84,80,105,83,80,80,82,69,83,78,78,73,75,73,120", "endOffsets": "271,354,449,547,632,713,819,903,984,1065,1148,1218,1302,1381,1460,1534,1610,1684,1805"}, "to": {"startLines": "10,11,12,13,14,15,16,75,76,77,78,79,80,81,82,83,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "898,987,1070,1165,1263,1348,1429,7885,7969,8050,8131,8214,8284,8368,8447,8526,8701,8777,8851", "endColumns": "88,82,94,97,84,80,105,83,80,80,82,69,83,78,78,73,75,73,120", "endOffsets": "982,1065,1160,1258,1343,1424,1530,7964,8045,8126,8209,8279,8363,8442,8521,8595,8772,8846,8967"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b59206c725e3c03c5728e46c84e8a9c8\\transformed\\foundation-release\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,136,220", "endColumns": "80,83,85", "endOffsets": "131,215,301"}, "to": {"startLines": "2,88,89", "startColumns": "4,4,4", "startOffsets": "105,8972,9056", "endColumns": "80,83,85", "endOffsets": "181,9051,9137"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9dddf8c9e00e2a90c0a65ed3f1ecb3d3\\transformed\\material3-release\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,290,400,516,614,719,842,979,1100,1243,1330,1435,1527,1627,1745,1871,1981,2127,2271,2408,2560,2686,2806,2929,3047,3140,3238,3361,3485,3585,3688,3796,3941,4091,4198,4300,4380,4474,4567,4684,4773,4858,4958,5037,5121,5222,5325,5424,5522,5609,5715,5815,5915,6044,6123,6224", "endColumns": "117,116,109,115,97,104,122,136,120,142,86,104,91,99,117,125,109,145,143,136,151,125,119,122,117,92,97,122,123,99,102,107,144,149,106,101,79,93,92,116,88,84,99,78,83,100,102,98,97,86,105,99,99,128,78,100,92", "endOffsets": "168,285,395,511,609,714,837,974,1095,1238,1325,1430,1522,1622,1740,1866,1976,2122,2266,2403,2555,2681,2801,2924,3042,3135,3233,3356,3480,3580,3683,3791,3936,4086,4193,4295,4375,4469,4562,4679,4768,4853,4953,5032,5116,5217,5320,5419,5517,5604,5710,5810,5910,6039,6118,6219,6312"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1535,1653,1770,1880,1996,2094,2199,2322,2459,2580,2723,2810,2915,3007,3107,3225,3351,3461,3607,3751,3888,4040,4166,4286,4409,4527,4620,4718,4841,4965,5065,5168,5276,5421,5571,5678,5780,5860,5954,6047,6164,6253,6338,6438,6517,6601,6702,6805,6904,7002,7089,7195,7295,7395,7524,7603,7704", "endColumns": "117,116,109,115,97,104,122,136,120,142,86,104,91,99,117,125,109,145,143,136,151,125,119,122,117,92,97,122,123,99,102,107,144,149,106,101,79,93,92,116,88,84,99,78,83,100,102,98,97,86,105,99,99,128,78,100,92", "endOffsets": "1648,1765,1875,1991,2089,2194,2317,2454,2575,2718,2805,2910,3002,3102,3220,3346,3456,3602,3746,3883,4035,4161,4281,4404,4522,4615,4713,4836,4960,5060,5163,5271,5416,5566,5673,5775,5855,5949,6042,6159,6248,6333,6433,6512,6596,6697,6800,6899,6997,7084,7190,7290,7390,7519,7598,7699,7792"}}]}]}