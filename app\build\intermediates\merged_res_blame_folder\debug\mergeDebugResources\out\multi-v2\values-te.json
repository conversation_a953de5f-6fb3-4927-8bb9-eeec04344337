{"logs": [{"outputFile": "com.Aries.AliyunCertTool.app-mergeDebugResources-54:/values-te/values-te.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\71e603f77f5431c60c0bf20ec43c96c0\\transformed\\material-release\\res\\values-te\\values-te.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "90", "endOffsets": "141"}, "to": {"startLines": "74", "startColumns": "4", "startOffsets": "8046", "endColumns": "90", "endOffsets": "8132"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9dddf8c9e00e2a90c0a65ed3f1ecb3d3\\transformed\\material3-release\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,184,315,429,559,663,762,878,1019,1131,1274,1358,1461,1557,1655,1771,1901,2009,2158,2305,2438,2634,2762,2878,2999,3136,3233,3330,3455,3583,3689,3795,3901,4044,4194,4302,4406,4482,4581,4682,4798,4892,4984,5091,5171,5254,5355,5483,5577,5689,5777,5888,5990,6107,6230,6310,6417", "endColumns": "128,130,113,129,103,98,115,140,111,142,83,102,95,97,115,129,107,148,146,132,195,127,115,120,136,96,96,124,127,105,105,105,142,149,107,103,75,98,100,115,93,91,106,79,82,100,127,93,111,87,110,101,116,122,79,106,96", "endOffsets": "179,310,424,554,658,757,873,1014,1126,1269,1353,1456,1552,1650,1766,1896,2004,2153,2300,2433,2629,2757,2873,2994,3131,3228,3325,3450,3578,3684,3790,3896,4039,4189,4297,4401,4477,4576,4677,4793,4887,4979,5086,5166,5249,5350,5478,5572,5684,5772,5883,5985,6102,6225,6305,6412,6509"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1587,1716,1847,1961,2091,2195,2294,2410,2551,2663,2806,2890,2993,3089,3187,3303,3433,3541,3690,3837,3970,4166,4294,4410,4531,4668,4765,4862,4987,5115,5221,5327,5433,5576,5726,5834,5938,6014,6113,6214,6330,6424,6516,6623,6703,6786,6887,7015,7109,7221,7309,7420,7522,7639,7762,7842,7949", "endColumns": "128,130,113,129,103,98,115,140,111,142,83,102,95,97,115,129,107,148,146,132,195,127,115,120,136,96,96,124,127,105,105,105,142,149,107,103,75,98,100,115,93,91,106,79,82,100,127,93,111,87,110,101,116,122,79,106,96", "endOffsets": "1711,1842,1956,2086,2190,2289,2405,2546,2658,2801,2885,2988,3084,3182,3298,3428,3536,3685,3832,3965,4161,4289,4405,4526,4663,4760,4857,4982,5110,5216,5322,5428,5571,5721,5829,5933,6009,6108,6209,6325,6419,6511,6618,6698,6781,6882,7010,7104,7216,7304,7415,7517,7634,7757,7837,7944,8041"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b59206c725e3c03c5728e46c84e8a9c8\\transformed\\foundation-release\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,129,217", "endColumns": "73,87,94", "endOffsets": "124,212,307"}, "to": {"startLines": "2,88,89", "startColumns": "4,4,4", "startOffsets": "105,9252,9340", "endColumns": "73,87,94", "endOffsets": "174,9335,9430"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\70c31411d223bd8dbd73cf3117964047\\transformed\\core-1.13.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,265,367,468,574,681,805", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "152,260,362,463,569,676,800,901"}, "to": {"startLines": "3,4,5,6,7,8,9,84", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "179,281,389,491,592,698,805,8888", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "276,384,486,587,693,800,924,8984"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\cf96ea3900c1f15eea4117a61e73d29e\\transformed\\ui-release\\res\\values-te\\values-te.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "180,278,367,464,564,653,742,838,926,1010,1094,1184,1261,1348,1430,1510,1589,1666,1735", "endColumns": "97,88,96,99,88,88,95,87,83,83,89,76,86,81,79,78,76,68,116", "endOffsets": "273,362,459,559,648,737,833,921,1005,1089,1179,1256,1343,1425,1505,1584,1661,1730,1847"}, "to": {"startLines": "10,11,12,13,14,15,16,75,76,77,78,79,80,81,82,83,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "929,1027,1116,1213,1313,1402,1491,8137,8225,8309,8393,8483,8560,8647,8729,8809,8989,9066,9135", "endColumns": "97,88,96,99,88,88,95,87,83,83,89,76,86,81,79,78,76,68,116", "endOffsets": "1022,1111,1208,1308,1397,1486,1582,8220,8304,8388,8478,8555,8642,8724,8804,8883,9061,9130,9247"}}]}]}