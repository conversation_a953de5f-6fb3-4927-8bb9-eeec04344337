{"logs": [{"outputFile": "com.Aries.AliyunCertTool.app-mergeDebugResources-53:/values-ta/values-ta.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\e5c03f6b4f5fe33a166d0205ccd1d4ac\\transformed\\material3-release\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,181,307,428,552,653,749,862,1013,1144,1285,1369,1473,1573,1681,1798,1921,2030,2176,2320,2454,2660,2789,2910,3035,3181,3282,3380,3526,3662,3768,3881,3988,4134,4286,4395,4507,4585,4687,4790,4907,4993,5086,5199,5279,5367,5466,5586,5681,5786,5875,5997,6101,6208,6341,6421,6532", "endColumns": "125,125,120,123,100,95,112,150,130,140,83,103,99,107,116,122,108,145,143,133,205,128,120,124,145,100,97,145,135,105,112,106,145,151,108,111,77,101,102,116,85,92,112,79,87,98,119,94,104,88,121,103,106,132,79,110,101", "endOffsets": "176,302,423,547,648,744,857,1008,1139,1280,1364,1468,1568,1676,1793,1916,2025,2171,2315,2449,2655,2784,2905,3030,3176,3277,3375,3521,3657,3763,3876,3983,4129,4281,4390,4502,4580,4682,4785,4902,4988,5081,5194,5274,5362,5461,5581,5676,5781,5870,5992,6096,6203,6336,6416,6527,6629"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1510,1636,1762,1883,2007,2108,2204,2317,2468,2599,2740,2824,2928,3028,3136,3253,3376,3485,3631,3775,3909,4115,4244,4365,4490,4636,4737,4835,4981,5117,5223,5336,5443,5589,5741,5850,5962,6040,6142,6245,6362,6448,6541,6654,6734,6822,6921,7041,7136,7241,7330,7452,7556,7663,7796,7876,7987", "endColumns": "125,125,120,123,100,95,112,150,130,140,83,103,99,107,116,122,108,145,143,133,205,128,120,124,145,100,97,145,135,105,112,106,145,151,108,111,77,101,102,116,85,92,112,79,87,98,119,94,104,88,121,103,106,132,79,110,101", "endOffsets": "1631,1757,1878,2002,2103,2199,2312,2463,2594,2735,2819,2923,3023,3131,3248,3371,3480,3626,3770,3904,4110,4239,4360,4485,4631,4732,4830,4976,5112,5218,5331,5438,5584,5736,5845,5957,6035,6137,6240,6357,6443,6536,6649,6729,6817,6916,7036,7131,7236,7325,7447,7551,7658,7791,7871,7982,8084"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\e810deac697feab8d2a0acfdc8f9cef5\\transformed\\ui-release\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,380,481,572,655,764,855,950,1032,1118,1208,1293,1366,1437,1517,1586", "endColumns": "96,83,93,100,90,82,108,90,94,81,85,89,84,72,70,79,68,119", "endOffsets": "197,281,375,476,567,650,759,850,945,1027,1113,1203,1288,1361,1432,1512,1581,1701"}, "to": {"startLines": "9,10,11,12,13,14,15,73,74,75,76,77,78,79,80,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "851,948,1032,1126,1227,1318,1401,8089,8180,8275,8357,8443,8533,8618,8691,8863,8943,9012", "endColumns": "96,83,93,100,90,82,108,90,94,81,85,89,84,72,70,79,68,119", "endOffsets": "943,1027,1121,1222,1313,1396,1505,8175,8270,8352,8438,8528,8613,8686,8757,8938,9007,9127"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\70c31411d223bd8dbd73cf3117964047\\transformed\\core-1.13.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,558,673,801", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "146,249,348,446,553,668,796,897"}, "to": {"startLines": "2,3,4,5,6,7,8,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,201,304,403,501,608,723,8762", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "196,299,398,496,603,718,846,8858"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a51497edcabbd2502676cd98d1603be0\\transformed\\foundation-release\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,148", "endColumns": "92,97", "endOffsets": "143,241"}, "to": {"startLines": "85,86", "startColumns": "4,4", "startOffsets": "9132,9225", "endColumns": "92,97", "endOffsets": "9220,9318"}}]}]}