{"logs": [{"outputFile": "com.Aries.AliyunCertTool.app-mergeDebugResources-54:/values-nl/values-nl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b59206c725e3c03c5728e46c84e8a9c8\\transformed\\foundation-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,142,228", "endColumns": "86,85,85", "endOffsets": "137,223,309"}, "to": {"startLines": "2,88,89", "startColumns": "4,4,4", "startOffsets": "105,9026,9112", "endColumns": "86,85,85", "endOffsets": "187,9107,9193"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\70c31411d223bd8dbd73cf3117964047\\transformed\\core-1.13.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,359,459,566,670,789", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "152,254,354,454,561,665,784,885"}, "to": {"startLines": "3,4,5,6,7,8,9,84", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "192,294,396,496,596,703,807,8651", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "289,391,491,591,698,802,921,8747"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\71e603f77f5431c60c0bf20ec43c96c0\\transformed\\material-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "89", "endOffsets": "140"}, "to": {"startLines": "74", "startColumns": "4", "startOffsets": "7840", "endColumns": "89", "endOffsets": "7925"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9dddf8c9e00e2a90c0a65ed3f1ecb3d3\\transformed\\material3-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,177,294,415,533,633,731,846,998,1119,1261,1346,1445,1541,1644,1762,1883,1987,2118,2246,2382,2560,2691,2811,2932,3067,3164,3264,3384,3513,3613,3720,3823,3960,4100,4206,4310,4394,4494,4591,4702,4789,4876,4981,5061,5144,5243,5347,5442,5541,5629,5739,5840,5945,6065,6145,6246", "endColumns": "121,116,120,117,99,97,114,151,120,141,84,98,95,102,117,120,103,130,127,135,177,130,119,120,134,96,99,119,128,99,106,102,136,139,105,103,83,99,96,110,86,86,104,79,82,98,103,94,98,87,109,100,104,119,79,100,94", "endOffsets": "172,289,410,528,628,726,841,993,1114,1256,1341,1440,1536,1639,1757,1878,1982,2113,2241,2377,2555,2686,2806,2927,3062,3159,3259,3379,3508,3608,3715,3818,3955,4095,4201,4305,4389,4489,4586,4697,4784,4871,4976,5056,5139,5238,5342,5437,5536,5624,5734,5835,5940,6060,6140,6241,6336"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1554,1676,1793,1914,2032,2132,2230,2345,2497,2618,2760,2845,2944,3040,3143,3261,3382,3486,3617,3745,3881,4059,4190,4310,4431,4566,4663,4763,4883,5012,5112,5219,5322,5459,5599,5705,5809,5893,5993,6090,6201,6288,6375,6480,6560,6643,6742,6846,6941,7040,7128,7238,7339,7444,7564,7644,7745", "endColumns": "121,116,120,117,99,97,114,151,120,141,84,98,95,102,117,120,103,130,127,135,177,130,119,120,134,96,99,119,128,99,106,102,136,139,105,103,83,99,96,110,86,86,104,79,82,98,103,94,98,87,109,100,104,119,79,100,94", "endOffsets": "1671,1788,1909,2027,2127,2225,2340,2492,2613,2755,2840,2939,3035,3138,3256,3377,3481,3612,3740,3876,4054,4185,4305,4426,4561,4658,4758,4878,5007,5107,5214,5317,5454,5594,5700,5804,5888,5988,6085,6196,6283,6370,6475,6555,6638,6737,6841,6936,7035,7123,7233,7334,7439,7559,7639,7740,7835"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\cf96ea3900c1f15eea4117a61e73d29e\\transformed\\ui-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "193,285,368,465,564,649,725,821,908,997,1078,1161,1238,1324,1399,1471,1542,1626,1696", "endColumns": "91,82,96,98,84,75,95,86,88,80,82,76,85,74,71,70,83,69,119", "endOffsets": "280,363,460,559,644,720,816,903,992,1073,1156,1233,1319,1394,1466,1537,1621,1691,1811"}, "to": {"startLines": "10,11,12,13,14,15,16,75,76,77,78,79,80,81,82,83,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "926,1018,1101,1198,1297,1382,1458,7930,8017,8106,8187,8270,8347,8433,8508,8580,8752,8836,8906", "endColumns": "91,82,96,98,84,75,95,86,88,80,82,76,85,74,71,70,83,69,119", "endOffsets": "1013,1096,1193,1292,1377,1453,1549,8012,8101,8182,8265,8342,8428,8503,8575,8646,8831,8901,9021"}}]}]}