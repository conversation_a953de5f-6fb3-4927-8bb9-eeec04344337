{"logs": [{"outputFile": "com.Aries.AliyunCertTool.app-mergeDebugResources-54:/values-sr/values-sr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9dddf8c9e00e2a90c0a65ed3f1ecb3d3\\transformed\\material3-release\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,287,414,530,628,722,833,969,1088,1230,1315,1415,1510,1608,1724,1849,1954,2095,2235,2368,2548,2673,2793,2918,3040,3136,3234,3351,3481,3581,3683,3792,3934,4083,4192,4295,4372,4470,4568,4677,4766,4852,4959,5039,5122,5219,5322,5415,5513,5600,5708,5805,5907,6040,6120,6227", "endColumns": "115,115,126,115,97,93,110,135,118,141,84,99,94,97,115,124,104,140,139,132,179,124,119,124,121,95,97,116,129,99,101,108,141,148,108,102,76,97,97,108,88,85,106,79,82,96,102,92,97,86,107,96,101,132,79,106,96", "endOffsets": "166,282,409,525,623,717,828,964,1083,1225,1310,1410,1505,1603,1719,1844,1949,2090,2230,2363,2543,2668,2788,2913,3035,3131,3229,3346,3476,3576,3678,3787,3929,4078,4187,4290,4367,4465,4563,4672,4761,4847,4954,5034,5117,5214,5317,5410,5508,5595,5703,5800,5902,6035,6115,6222,6319"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1555,1671,1787,1914,2030,2128,2222,2333,2469,2588,2730,2815,2915,3010,3108,3224,3349,3454,3595,3735,3868,4048,4173,4293,4418,4540,4636,4734,4851,4981,5081,5183,5292,5434,5583,5692,5795,5872,5970,6068,6177,6266,6352,6459,6539,6622,6719,6822,6915,7013,7100,7208,7305,7407,7540,7620,7727", "endColumns": "115,115,126,115,97,93,110,135,118,141,84,99,94,97,115,124,104,140,139,132,179,124,119,124,121,95,97,116,129,99,101,108,141,148,108,102,76,97,97,108,88,85,106,79,82,96,102,92,97,86,107,96,101,132,79,106,96", "endOffsets": "1666,1782,1909,2025,2123,2217,2328,2464,2583,2725,2810,2910,3005,3103,3219,3344,3449,3590,3730,3863,4043,4168,4288,4413,4535,4631,4729,4846,4976,5076,5178,5287,5429,5578,5687,5790,5867,5965,6063,6172,6261,6347,6454,6534,6617,6714,6817,6910,7008,7095,7203,7300,7402,7535,7615,7722,7819"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\cf96ea3900c1f15eea4117a61e73d29e\\transformed\\ui-release\\res\\values-sr\\values-sr.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "194,291,378,475,576,662,739,830,922,1007,1087,1172,1245,1334,1411,1489,1565,1644,1714", "endColumns": "96,86,96,100,85,76,90,91,84,79,84,72,88,76,77,75,78,69,117", "endOffsets": "286,373,470,571,657,734,825,917,1002,1082,1167,1240,1329,1406,1484,1560,1639,1709,1827"}, "to": {"startLines": "10,11,12,13,14,15,16,75,76,77,78,79,80,81,82,83,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "919,1016,1103,1200,1301,1387,1464,7917,8009,8094,8174,8259,8332,8421,8498,8576,8753,8832,8902", "endColumns": "96,86,96,100,85,76,90,91,84,79,84,72,88,76,77,75,78,69,117", "endOffsets": "1011,1098,1195,1296,1382,1459,1550,8004,8089,8169,8254,8327,8416,8493,8571,8647,8827,8897,9015"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b59206c725e3c03c5728e46c84e8a9c8\\transformed\\foundation-release\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,143,231", "endColumns": "87,87,89", "endOffsets": "138,226,316"}, "to": {"startLines": "2,88,89", "startColumns": "4,4,4", "startOffsets": "105,9020,9108", "endColumns": "87,87,89", "endOffsets": "188,9103,9193"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\70c31411d223bd8dbd73cf3117964047\\transformed\\core-1.13.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "3,4,5,6,7,8,9,84", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "193,291,393,490,594,698,803,8652", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "286,388,485,589,693,798,914,8748"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\71e603f77f5431c60c0bf20ec43c96c0\\transformed\\material-release\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "92", "endOffsets": "143"}, "to": {"startLines": "74", "startColumns": "4", "startOffsets": "7824", "endColumns": "92", "endOffsets": "7912"}}]}]}