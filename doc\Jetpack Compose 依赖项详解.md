# Jetpack Compose 依赖项详解

本文档详细解释了项目中每个 Jetpack Compose 依赖项的作用，适合初学者理解。

## 📚 依赖项分类说明

### 🎯 1. BOM (版本管理)
```kotlin
implementation(platform(libs.androidx.compose.bom))
```
**作用**: BOM (Bill of Materials) 是版本管理工具，确保所有 Compose 库使用兼容的版本。
**比喻**: 就像一个"版本协调员"，确保所有组件能够完美配合工作。

### 🎨 2. UI 设计系统

#### Material Design 3 (推荐)
```kotlin
implementation(libs.androidx.material3)
```
**作用**: Google 最新的设计语言，提供现代化的 UI 组件
**包含组件**: Button、Card、TextField、TopAppBar、NavigationBar 等
**特点**: 支持动态颜色、更好的无障碍功能

#### Foundation (基础组件)
```kotlin
implementation(libs.androidx.compose.foundation)
```
**作用**: Compose 的基础构建块，用于创建自定义组件
**包含功能**: 
- 布局 (Layout)
- 手势处理 (Gestures)
- 绘制 (Drawing)
- 动画 (Animation)

### 🔧 3. 核心 UI 组件

#### UI 核心
```kotlin
implementation(libs.androidx.ui)
```
**作用**: 最基础的 UI 组件
**包含**: Text、Button、Column、Row、Box、Spacer 等

#### UI 图形
```kotlin
implementation(libs.androidx.ui.graphics)
```
**作用**: 处理图形相关功能
**包含**: Color、Brush、Path、Canvas 等

### 🛠️ 4. 开发工具

#### 预览工具
```kotlin
implementation(libs.androidx.ui.tooling.preview)
debugImplementation(libs.androidx.ui.tooling)
```
**作用**: 在 Android Studio 中实时预览 UI
**使用方法**: 在函数上添加 `@Preview` 注解

### 🧪 5. 测试工具

#### UI 测试
```kotlin
androidTestImplementation(libs.androidx.ui.test.junit4)
debugImplementation(libs.androidx.ui.test.manifest)
```
**作用**: 编写和运行 UI 自动化测试
**用途**: 测试用户交互、UI 状态变化等

### 🎭 6. 图标库

#### 核心图标 (约 1000+ 个)
```kotlin
implementation(libs.androidx.material.icons.core)
```
**包含**: 最常用的图标 (home、search、menu 等)

#### 扩展图标 (约 5000+ 个)
```kotlin
implementation(libs.androidx.material.icons)
```
**包含**: 完整的 Material 图标集

### 📱 7. 响应式设计

#### Material3 Adaptive
```kotlin
implementation(libs.androidx.material3.adaptive)
```
**作用**: 帮助应用适配不同屏幕尺寸
**支持**: 手机、平板、折叠屏、桌面

### 🏗️ 8. 架构组件集成

#### Activity 集成
```kotlin
implementation(libs.androidx.activity.compose)
```
**作用**: 让传统的 Activity 可以使用 Compose UI
**提供**: `setContent {}` 函数

#### ViewModel 集成
```kotlin
implementation(libs.androidx.lifecycle.viewmodel.compose)
```
**作用**: 在 Compose 中使用 ViewModel 管理状态
**提供**: `viewModel()` 函数

#### LiveData 集成
```kotlin
implementation(libs.androidx.compose.runtime.livedata)
```
**作用**: 将 LiveData 转换为 Compose State
**提供**: `observeAsState()` 函数

## 🚀 初学者使用建议

### 必需依赖 (一定要有)
- ✅ Compose BOM
- ✅ Material3
- ✅ UI 核心组件
- ✅ Activity 集成
- ✅ 预览工具

### 推荐依赖 (建议添加)
- 🔄 Foundation (自定义组件)
- 🔄 ViewModel 集成 (状态管理)
- 🔄 图标库 (UI 美化)

### 可选依赖 (按需添加)
- 💡 LiveData 集成 (如果使用 LiveData)
- 💡 RxJava 集成 (如果使用 RxJava)
- 💡 Material3 Adaptive (多屏幕适配)

## 📖 学习路径建议

1. **第一步**: 熟悉基础组件 (Text, Button, Column, Row)
2. **第二步**: 学习 Material3 组件 (Card, TextField, TopAppBar)
3. **第三步**: 掌握状态管理 (ViewModel + Compose)
4. **第四步**: 学习自定义组件 (Foundation)
5. **第五步**: 响应式设计 (Adaptive)

## 🔍 常用代码示例

### 基础 UI
```kotlin
@Composable
fun MyScreen() {
    Column {
        Text("Hello Compose!")
        Button(onClick = { }) {
            Text("Click me")
        }
    }
}
```

### 使用图标
```kotlin
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Home

Icon(
    imageVector = Icons.Default.Home,
    contentDescription = "Home"
)
```

### ViewModel 集成
```kotlin
@Composable
fun MyScreen(viewModel: MyViewModel = viewModel()) {
    val uiState by viewModel.uiState.collectAsState()
    // 使用 uiState
}
```
