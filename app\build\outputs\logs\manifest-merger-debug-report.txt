-- Merging decision tree log ---
manifest
ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:2:1-28:12
INJECTED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:2:1-28:12
INJECTED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:2:1-28:12
INJECTED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:2:1-28:12
MERGED from [androidx.compose.material3:material3-android:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-4\9dddf8c9e00e2a90c0a65ed3f1ecb3d3\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material3.adaptive:adaptive-android:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\5d9b46568afb74f79c18eaaf23f38f3b\transformed\adaptive-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\71e603f77f5431c60c0bf20ec43c96c0\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\c019cb3ccd359a3c27ad67ebb26d0f5e\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5cdf3c2dc6c831e9ee8703cf0c82dcb9\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5acd00b58402e7b02bfbf420995e50eb\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\74c9c59c2a0dfb61422b952d0d91bdb3\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\b59206c725e3c03c5728e46c84e8a9c8\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\58f26da3a228f701e80886144cbe65ec\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\90f0e0cde63389927e8b5de596c788ab\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\e3fe1e5aa17ee7f492aa07f27a624c28\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\f701097230653d55b3e11dcbb19f8652\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\e9ed3dc608a0c6cb7c9a2aa3f2655d48\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\64193461a3f5ad5bc28f01eb68a22ff9\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\fa6194f87e2a819412e48af269513aee\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\1bb331d492c019dcf26e1da7719e21ab\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\b0843a9727e1c6b00d1ab50239f395df\transformed\ui-test-manifest-1.8.1\AndroidManifest.xml:17:1-29:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.8] C:\Users\<USER>\.gradle\caches\transforms-4\e5d0beee618fb76e9256ccc0949eade8\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\transforms-4\b435f944077037c69c870c98e042de99\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c285cc7c22de9bc40b82cb5a583176a\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\70cc88ca3a4c4064e70b0aa04b742af2\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-livedata:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\06486275994e20e7e91089a17ba6024f\transformed\runtime-livedata-1.8.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-4\f1ab4ef3899c4192b0e0334f75282d3a\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\12eda9bd7e872c2ad2d73c065558b629\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\a56d10200708944b8fc73cc8e55f3ae2\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window:window-core-android:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\74566099baaee03a727c517824d662a2\transformed\window-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b5ca4484bc6448589137ef4a4606beed\transformed\window-1.3.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1a29050001e3ee976279fd3e69913833\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\a34eafa8a101c38b0e008228001a6cf6\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a6ebdcd9fe3662fa7e0f8e75496e5f16\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\d11950747741772ac25b3a31e77760ee\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\be21046955a2cf2f08dc611156219bcb\transformed\emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\b7a645b4501d9bd8a893093c15e3c8d8\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\8d2e6e481c3f7c09a71606b5dc967973\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\99ec7351f88673f7e3413c630d55da71\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\10be72bd39a67899004c87cd30116fa0\transformed\lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\256c9e33227456921dd498fb4b08e03f\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\d262f2e26b24a6ffe6e9e498d466b8e2\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\86b6fafe794994fcb34fe52c69f1577d\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\0de59ce3c0beaf628d0c1f596e27f0c0\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\1c2e381b0074aa5bf08fbef698b4379b\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\194c733b5b2fbbea1cb95e964873b44a\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\fce9ce67adba600a889e22ee9bad3ca4\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\cf96ea3900c1f15eea4117a61e73d29e\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-4\a8fbec379281e5d1b53a86a29dbd1cba\transformed\activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-4\6ba0a1bfd3c6b9bddd07fb3af083f391\transformed\activity-compose-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\4d28bde63148b6b18eb39174d8cf0994\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a435ffc347de873fb9a97b1b06faac5f\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\fcfbb0aa032cdca161ce213b34c37851\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a3a70dcdbdfd4674961d840abf29097\transformed\core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\8eb842c10b8c11b900558b9b1d222889\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\241ac0666bab5c06f579dcdd49058e31\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b513cf0c6c80823111cf115846e490d5\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\298b31876c0b07be2665ab2b97c6a6dc\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:2:11-69
application
ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:5:5-26:19
INJECTED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:5:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\fa6194f87e2a819412e48af269513aee\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\fa6194f87e2a819412e48af269513aee\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\b0843a9727e1c6b00d1ab50239f395df\transformed\ui-test-manifest-1.8.1\AndroidManifest.xml:22:5-27:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\b0843a9727e1c6b00d1ab50239f395df\transformed\ui-test-manifest-1.8.1\AndroidManifest.xml:22:5-27:19
MERGED from [androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b5ca4484bc6448589137ef4a4606beed\transformed\window-1.3.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b5ca4484bc6448589137ef4a4606beed\transformed\window-1.3.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\be21046955a2cf2f08dc611156219bcb\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\be21046955a2cf2f08dc611156219bcb\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\b7a645b4501d9bd8a893093c15e3c8d8\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\b7a645b4501d9bd8a893093c15e3c8d8\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\fcfbb0aa032cdca161ce213b34c37851\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\fcfbb0aa032cdca161ce213b34c37851\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\8eb842c10b8c11b900558b9b1d222889\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\8eb842c10b8c11b900558b9b1d222889\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:12:9-35
	android:label
		ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:10:9-41
	android:fullBackupContent
		ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:8:9-54
	android:roundIcon
		ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:11:9-54
	tools:targetApi
		ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:14:9-29
	android:icon
		ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:9:9-43
	android:allowBackup
		ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:6:9-35
	android:theme
		ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:13:9-52
	android:dataExtractionRules
		ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:7:9-65
activity#com.Aries.AliyunCertTool.MainActivity
ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:15:9-25:20
	android:label
		ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:18:13-45
	android:exported
		ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:17:13-36
	android:theme
		ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:19:13-56
	android:name
		ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:16:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:20:13-24:29
action#android.intent.action.MAIN
ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:21:17-69
	android:name
		ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:21:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:23:17-77
	android:name
		ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:23:27-74
uses-sdk
INJECTED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml
INJECTED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml
MERGED from [androidx.compose.material3:material3-android:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-4\9dddf8c9e00e2a90c0a65ed3f1ecb3d3\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-4\9dddf8c9e00e2a90c0a65ed3f1ecb3d3\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3.adaptive:adaptive-android:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\5d9b46568afb74f79c18eaaf23f38f3b\transformed\adaptive-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3.adaptive:adaptive-android:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\5d9b46568afb74f79c18eaaf23f38f3b\transformed\adaptive-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\71e603f77f5431c60c0bf20ec43c96c0\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\71e603f77f5431c60c0bf20ec43c96c0\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\c019cb3ccd359a3c27ad67ebb26d0f5e\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\c019cb3ccd359a3c27ad67ebb26d0f5e\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5cdf3c2dc6c831e9ee8703cf0c82dcb9\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5cdf3c2dc6c831e9ee8703cf0c82dcb9\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5acd00b58402e7b02bfbf420995e50eb\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5acd00b58402e7b02bfbf420995e50eb\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\74c9c59c2a0dfb61422b952d0d91bdb3\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\74c9c59c2a0dfb61422b952d0d91bdb3\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\b59206c725e3c03c5728e46c84e8a9c8\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\b59206c725e3c03c5728e46c84e8a9c8\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\58f26da3a228f701e80886144cbe65ec\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\58f26da3a228f701e80886144cbe65ec\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\90f0e0cde63389927e8b5de596c788ab\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\90f0e0cde63389927e8b5de596c788ab\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\e3fe1e5aa17ee7f492aa07f27a624c28\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\e3fe1e5aa17ee7f492aa07f27a624c28\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\f701097230653d55b3e11dcbb19f8652\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\f701097230653d55b3e11dcbb19f8652\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\e9ed3dc608a0c6cb7c9a2aa3f2655d48\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\e9ed3dc608a0c6cb7c9a2aa3f2655d48\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\64193461a3f5ad5bc28f01eb68a22ff9\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\64193461a3f5ad5bc28f01eb68a22ff9\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\fa6194f87e2a819412e48af269513aee\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\fa6194f87e2a819412e48af269513aee\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\1bb331d492c019dcf26e1da7719e21ab\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\1bb331d492c019dcf26e1da7719e21ab\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\b0843a9727e1c6b00d1ab50239f395df\transformed\ui-test-manifest-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\b0843a9727e1c6b00d1ab50239f395df\transformed\ui-test-manifest-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.8] C:\Users\<USER>\.gradle\caches\transforms-4\e5d0beee618fb76e9256ccc0949eade8\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.8] C:\Users\<USER>\.gradle\caches\transforms-4\e5d0beee618fb76e9256ccc0949eade8\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\transforms-4\b435f944077037c69c870c98e042de99\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\transforms-4\b435f944077037c69c870c98e042de99\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c285cc7c22de9bc40b82cb5a583176a\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c285cc7c22de9bc40b82cb5a583176a\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\70cc88ca3a4c4064e70b0aa04b742af2\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\70cc88ca3a4c4064e70b0aa04b742af2\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-livedata:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\06486275994e20e7e91089a17ba6024f\transformed\runtime-livedata-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-livedata:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\06486275994e20e7e91089a17ba6024f\transformed\runtime-livedata-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-4\f1ab4ef3899c4192b0e0334f75282d3a\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-4\f1ab4ef3899c4192b0e0334f75282d3a\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\12eda9bd7e872c2ad2d73c065558b629\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\12eda9bd7e872c2ad2d73c065558b629\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\a56d10200708944b8fc73cc8e55f3ae2\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\a56d10200708944b8fc73cc8e55f3ae2\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-core-android:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\74566099baaee03a727c517824d662a2\transformed\window-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.window:window-core-android:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\74566099baaee03a727c517824d662a2\transformed\window-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b5ca4484bc6448589137ef4a4606beed\transformed\window-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b5ca4484bc6448589137ef4a4606beed\transformed\window-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1a29050001e3ee976279fd3e69913833\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1a29050001e3ee976279fd3e69913833\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\a34eafa8a101c38b0e008228001a6cf6\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\a34eafa8a101c38b0e008228001a6cf6\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a6ebdcd9fe3662fa7e0f8e75496e5f16\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a6ebdcd9fe3662fa7e0f8e75496e5f16\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\d11950747741772ac25b3a31e77760ee\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\d11950747741772ac25b3a31e77760ee\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\be21046955a2cf2f08dc611156219bcb\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\be21046955a2cf2f08dc611156219bcb\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\b7a645b4501d9bd8a893093c15e3c8d8\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\b7a645b4501d9bd8a893093c15e3c8d8\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\8d2e6e481c3f7c09a71606b5dc967973\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\8d2e6e481c3f7c09a71606b5dc967973\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\99ec7351f88673f7e3413c630d55da71\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\99ec7351f88673f7e3413c630d55da71\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\10be72bd39a67899004c87cd30116fa0\transformed\lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\10be72bd39a67899004c87cd30116fa0\transformed\lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\256c9e33227456921dd498fb4b08e03f\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\256c9e33227456921dd498fb4b08e03f\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\d262f2e26b24a6ffe6e9e498d466b8e2\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\d262f2e26b24a6ffe6e9e498d466b8e2\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\86b6fafe794994fcb34fe52c69f1577d\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\86b6fafe794994fcb34fe52c69f1577d\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\0de59ce3c0beaf628d0c1f596e27f0c0\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\0de59ce3c0beaf628d0c1f596e27f0c0\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\1c2e381b0074aa5bf08fbef698b4379b\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\1c2e381b0074aa5bf08fbef698b4379b\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\194c733b5b2fbbea1cb95e964873b44a\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\194c733b5b2fbbea1cb95e964873b44a\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\fce9ce67adba600a889e22ee9bad3ca4\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\fce9ce67adba600a889e22ee9bad3ca4\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\cf96ea3900c1f15eea4117a61e73d29e\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\cf96ea3900c1f15eea4117a61e73d29e\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-4\a8fbec379281e5d1b53a86a29dbd1cba\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-4\a8fbec379281e5d1b53a86a29dbd1cba\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-4\6ba0a1bfd3c6b9bddd07fb3af083f391\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-4\6ba0a1bfd3c6b9bddd07fb3af083f391\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\4d28bde63148b6b18eb39174d8cf0994\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\4d28bde63148b6b18eb39174d8cf0994\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a435ffc347de873fb9a97b1b06faac5f\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a435ffc347de873fb9a97b1b06faac5f\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\fcfbb0aa032cdca161ce213b34c37851\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\fcfbb0aa032cdca161ce213b34c37851\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a3a70dcdbdfd4674961d840abf29097\transformed\core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a3a70dcdbdfd4674961d840abf29097\transformed\core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\8eb842c10b8c11b900558b9b1d222889\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\8eb842c10b8c11b900558b9b1d222889\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\241ac0666bab5c06f579dcdd49058e31\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\241ac0666bab5c06f579dcdd49058e31\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b513cf0c6c80823111cf115846e490d5\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b513cf0c6c80823111cf115846e490d5\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\298b31876c0b07be2665ab2b97c6a6dc\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\298b31876c0b07be2665ab2b97c6a6dc\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\fa6194f87e2a819412e48af269513aee\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\fa6194f87e2a819412e48af269513aee\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\fa6194f87e2a819412e48af269513aee\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\b0843a9727e1c6b00d1ab50239f395df\transformed\ui-test-manifest-1.8.1\AndroidManifest.xml:23:9-26:79
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\b0843a9727e1c6b00d1ab50239f395df\transformed\ui-test-manifest-1.8.1\AndroidManifest.xml:25:13-36
	android:theme
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\b0843a9727e1c6b00d1ab50239f395df\transformed\ui-test-manifest-1.8.1\AndroidManifest.xml:26:13-76
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\b0843a9727e1c6b00d1ab50239f395df\transformed\ui-test-manifest-1.8.1\AndroidManifest.xml:24:13-63
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b5ca4484bc6448589137ef4a4606beed\transformed\window-1.3.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b5ca4484bc6448589137ef4a4606beed\transformed\window-1.3.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b5ca4484bc6448589137ef4a4606beed\transformed\window-1.3.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b5ca4484bc6448589137ef4a4606beed\transformed\window-1.3.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b5ca4484bc6448589137ef4a4606beed\transformed\window-1.3.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b5ca4484bc6448589137ef4a4606beed\transformed\window-1.3.0\AndroidManifest.xml:27:13-51
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\be21046955a2cf2f08dc611156219bcb\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\b7a645b4501d9bd8a893093c15e3c8d8\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\b7a645b4501d9bd8a893093c15e3c8d8\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\8eb842c10b8c11b900558b9b1d222889\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\8eb842c10b8c11b900558b9b1d222889\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\be21046955a2cf2f08dc611156219bcb\transformed\emoji2-1.4.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\be21046955a2cf2f08dc611156219bcb\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\be21046955a2cf2f08dc611156219bcb\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\be21046955a2cf2f08dc611156219bcb\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\be21046955a2cf2f08dc611156219bcb\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\be21046955a2cf2f08dc611156219bcb\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\be21046955a2cf2f08dc611156219bcb\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.Aries.AliyunCertTool.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.Aries.AliyunCertTool.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\b7a645b4501d9bd8a893093c15e3c8d8\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\b7a645b4501d9bd8a893093c15e3c8d8\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\b7a645b4501d9bd8a893093c15e3c8d8\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2b0aba8f03727902186fb142f5b4c7df\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
