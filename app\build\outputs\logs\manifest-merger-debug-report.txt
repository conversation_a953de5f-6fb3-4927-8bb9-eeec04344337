-- Merging decision tree log ---
manifest
ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:2:1-28:12
INJECTED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:2:1-28:12
INJECTED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:2:1-28:12
INJECTED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:2:1-28:12
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e5c03f6b4f5fe33a166d0205ccd1d4ac\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material3.adaptive:adaptive-android:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8eb6bd2c23e89b432d78a8957c60a0de\transformed\adaptive-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\07da6824b46a2f66b2b5d82e761a9fc8\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\f32d9953c2210fdc71d4f6fd64f370ea\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\7bdaee99d5e8f4a6568329ceab5e506f\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\7f444c1dcf1e5b90f25a32d8f17104af\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\eeb65cdc6d95c6444f70dfc000062658\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\a51497edcabbd2502676cd98d1603be0\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\87e26a2f43bae6e21a0969e683235ddf\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\f4407436cc175a4349d3b46ab9a67c32\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\6f28d906a8ac7020da7123e7cbb7b707\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\85cd9215543cd28236fad1d95671d314\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\ab8e1d07a79fef2818aab3fc8ec50718\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\6cc2123d924a4bc514bf705d1358395a\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\75f659e769d841329b7f2d2243cabfd5\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\89cde597d5165d323f3b1e7d17e26cc4\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\b1e63eb5eb0681e2e793bedfd5b2b54c\transformed\ui-test-manifest-1.7.3\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.activity:activity:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\d294e7ab832ca6f45f919801eb74ab9b\transformed\activity-1.9.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\ebe3a4f11cddbbad3f03bc17eeda1bd3\transformed\activity-ktx-1.9.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\78a529e343ee72823aa28f8bb7aad4b1\transformed\activity-compose-1.9.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\425ab76ee7f8e614eca3434044845c18\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cdf3e867b1382a5d5b2e8e0e042d4c1c\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.window:window-core-android:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\74566099baaee03a727c517824d662a2\transformed\window-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b5ca4484bc6448589137ef4a4606beed\transformed\window-1.3.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1a29050001e3ee976279fd3e69913833\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\a34eafa8a101c38b0e008228001a6cf6\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a6ebdcd9fe3662fa7e0f8e75496e5f16\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\d11950747741772ac25b3a31e77760ee\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\12eda9bd7e872c2ad2d73c065558b629\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\a56d10200708944b8fc73cc8e55f3ae2\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\8b095931678b4e60ac63c99c8bf47483\transformed\lifecycle-process-2.8.5\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\c1e0b0b0abd029e647c573810f9c0ff4\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\b3a49040c74edc5e4177638a7355a843\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\45fb77f59cf5f7720db5722cca3de76a\transformed\lifecycle-livedata-core-ktx-2.8.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\bb203f674a456f9c10f2c310d90ca7f1\transformed\lifecycle-livedata-core-2.8.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\7d3a4f80790e465d9b992b6fa510745d\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\5572fcd325640d9a6acd78c6b28a09e3\transformed\lifecycle-viewmodel-savedstate-2.8.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\1fac29ca217bbe75777611559e5a5fc6\transformed\lifecycle-viewmodel-ktx-2.8.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\0df0c339549ded8b509e58fba4bb0105\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\3a872c309ad2c08cb10eca6a291bca96\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-livedata:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\8f40460db96f1709e87dbe869fcb8ff1\transformed\runtime-livedata-1.7.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\ad37ffafad910c6ea5d86cb904c4105e\transformed\lifecycle-livedata-2.8.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\fc70e012532b6eb0e385febc5aaf0635\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\57ff1b16d28c33f8d063d90a75f7b537\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\1709a919c83892f327f230de2091f244\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\e810deac697feab8d2a0acfdc8f9cef5\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\4d28bde63148b6b18eb39174d8cf0994\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a3a70dcdbdfd4674961d840abf29097\transformed\core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\fcfbb0aa032cdca161ce213b34c37851\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\8eb842c10b8c11b900558b9b1d222889\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\241ac0666bab5c06f579dcdd49058e31\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\298b31876c0b07be2665ab2b97c6a6dc\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b513cf0c6c80823111cf115846e490d5\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:2:11-69
application
ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:5:5-26:19
INJECTED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:5:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\75f659e769d841329b7f2d2243cabfd5\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\75f659e769d841329b7f2d2243cabfd5\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\b1e63eb5eb0681e2e793bedfd5b2b54c\transformed\ui-test-manifest-1.7.3\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\b1e63eb5eb0681e2e793bedfd5b2b54c\transformed\ui-test-manifest-1.7.3\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cdf3e867b1382a5d5b2e8e0e042d4c1c\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cdf3e867b1382a5d5b2e8e0e042d4c1c\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b5ca4484bc6448589137ef4a4606beed\transformed\window-1.3.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b5ca4484bc6448589137ef4a4606beed\transformed\window-1.3.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\8b095931678b4e60ac63c99c8bf47483\transformed\lifecycle-process-2.8.5\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\8b095931678b4e60ac63c99c8bf47483\transformed\lifecycle-process-2.8.5\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\fcfbb0aa032cdca161ce213b34c37851\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\fcfbb0aa032cdca161ce213b34c37851\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\8eb842c10b8c11b900558b9b1d222889\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\8eb842c10b8c11b900558b9b1d222889\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:12:9-35
	android:label
		ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:10:9-41
	android:fullBackupContent
		ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:8:9-54
	android:roundIcon
		ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:11:9-54
	tools:targetApi
		ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:14:9-29
	android:icon
		ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:9:9-43
	android:allowBackup
		ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:6:9-35
	android:theme
		ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:13:9-52
	android:dataExtractionRules
		ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:7:9-65
activity#com.Aries.AliyunCertTool.MainActivity
ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:15:9-25:20
	android:label
		ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:18:13-45
	android:exported
		ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:17:13-36
	android:theme
		ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:19:13-56
	android:name
		ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:16:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:20:13-24:29
action#android.intent.action.MAIN
ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:21:17-69
	android:name
		ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:21:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:23:17-77
	android:name
		ADDED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml:23:27-74
uses-sdk
INJECTED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml
INJECTED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e5c03f6b4f5fe33a166d0205ccd1d4ac\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e5c03f6b4f5fe33a166d0205ccd1d4ac\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3.adaptive:adaptive-android:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8eb6bd2c23e89b432d78a8957c60a0de\transformed\adaptive-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3.adaptive:adaptive-android:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8eb6bd2c23e89b432d78a8957c60a0de\transformed\adaptive-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\07da6824b46a2f66b2b5d82e761a9fc8\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\07da6824b46a2f66b2b5d82e761a9fc8\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\f32d9953c2210fdc71d4f6fd64f370ea\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\f32d9953c2210fdc71d4f6fd64f370ea\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\7bdaee99d5e8f4a6568329ceab5e506f\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\7bdaee99d5e8f4a6568329ceab5e506f\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\7f444c1dcf1e5b90f25a32d8f17104af\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\7f444c1dcf1e5b90f25a32d8f17104af\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\eeb65cdc6d95c6444f70dfc000062658\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\eeb65cdc6d95c6444f70dfc000062658\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\a51497edcabbd2502676cd98d1603be0\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\a51497edcabbd2502676cd98d1603be0\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\87e26a2f43bae6e21a0969e683235ddf\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\87e26a2f43bae6e21a0969e683235ddf\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\f4407436cc175a4349d3b46ab9a67c32\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\f4407436cc175a4349d3b46ab9a67c32\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\6f28d906a8ac7020da7123e7cbb7b707\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\6f28d906a8ac7020da7123e7cbb7b707\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\85cd9215543cd28236fad1d95671d314\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\85cd9215543cd28236fad1d95671d314\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\ab8e1d07a79fef2818aab3fc8ec50718\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\ab8e1d07a79fef2818aab3fc8ec50718\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\6cc2123d924a4bc514bf705d1358395a\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\6cc2123d924a4bc514bf705d1358395a\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\75f659e769d841329b7f2d2243cabfd5\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\75f659e769d841329b7f2d2243cabfd5\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\89cde597d5165d323f3b1e7d17e26cc4\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\89cde597d5165d323f3b1e7d17e26cc4\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\b1e63eb5eb0681e2e793bedfd5b2b54c\transformed\ui-test-manifest-1.7.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\b1e63eb5eb0681e2e793bedfd5b2b54c\transformed\ui-test-manifest-1.7.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\d294e7ab832ca6f45f919801eb74ab9b\transformed\activity-1.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\d294e7ab832ca6f45f919801eb74ab9b\transformed\activity-1.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\ebe3a4f11cddbbad3f03bc17eeda1bd3\transformed\activity-ktx-1.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\ebe3a4f11cddbbad3f03bc17eeda1bd3\transformed\activity-ktx-1.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\78a529e343ee72823aa28f8bb7aad4b1\transformed\activity-compose-1.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\78a529e343ee72823aa28f8bb7aad4b1\transformed\activity-compose-1.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\425ab76ee7f8e614eca3434044845c18\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\425ab76ee7f8e614eca3434044845c18\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cdf3e867b1382a5d5b2e8e0e042d4c1c\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cdf3e867b1382a5d5b2e8e0e042d4c1c\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.window:window-core-android:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\74566099baaee03a727c517824d662a2\transformed\window-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.window:window-core-android:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\74566099baaee03a727c517824d662a2\transformed\window-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b5ca4484bc6448589137ef4a4606beed\transformed\window-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b5ca4484bc6448589137ef4a4606beed\transformed\window-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1a29050001e3ee976279fd3e69913833\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1a29050001e3ee976279fd3e69913833\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\a34eafa8a101c38b0e008228001a6cf6\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\a34eafa8a101c38b0e008228001a6cf6\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a6ebdcd9fe3662fa7e0f8e75496e5f16\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a6ebdcd9fe3662fa7e0f8e75496e5f16\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\d11950747741772ac25b3a31e77760ee\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\d11950747741772ac25b3a31e77760ee\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\12eda9bd7e872c2ad2d73c065558b629\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\12eda9bd7e872c2ad2d73c065558b629\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\a56d10200708944b8fc73cc8e55f3ae2\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\a56d10200708944b8fc73cc8e55f3ae2\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\8b095931678b4e60ac63c99c8bf47483\transformed\lifecycle-process-2.8.5\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\8b095931678b4e60ac63c99c8bf47483\transformed\lifecycle-process-2.8.5\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\c1e0b0b0abd029e647c573810f9c0ff4\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\c1e0b0b0abd029e647c573810f9c0ff4\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\b3a49040c74edc5e4177638a7355a843\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\b3a49040c74edc5e4177638a7355a843\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\45fb77f59cf5f7720db5722cca3de76a\transformed\lifecycle-livedata-core-ktx-2.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\45fb77f59cf5f7720db5722cca3de76a\transformed\lifecycle-livedata-core-ktx-2.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\bb203f674a456f9c10f2c310d90ca7f1\transformed\lifecycle-livedata-core-2.8.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\bb203f674a456f9c10f2c310d90ca7f1\transformed\lifecycle-livedata-core-2.8.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\7d3a4f80790e465d9b992b6fa510745d\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\7d3a4f80790e465d9b992b6fa510745d\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\5572fcd325640d9a6acd78c6b28a09e3\transformed\lifecycle-viewmodel-savedstate-2.8.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\5572fcd325640d9a6acd78c6b28a09e3\transformed\lifecycle-viewmodel-savedstate-2.8.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\1fac29ca217bbe75777611559e5a5fc6\transformed\lifecycle-viewmodel-ktx-2.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\1fac29ca217bbe75777611559e5a5fc6\transformed\lifecycle-viewmodel-ktx-2.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\0df0c339549ded8b509e58fba4bb0105\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\0df0c339549ded8b509e58fba4bb0105\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\3a872c309ad2c08cb10eca6a291bca96\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\3a872c309ad2c08cb10eca6a291bca96\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-livedata:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\8f40460db96f1709e87dbe869fcb8ff1\transformed\runtime-livedata-1.7.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-livedata:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\8f40460db96f1709e87dbe869fcb8ff1\transformed\runtime-livedata-1.7.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\ad37ffafad910c6ea5d86cb904c4105e\transformed\lifecycle-livedata-2.8.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\ad37ffafad910c6ea5d86cb904c4105e\transformed\lifecycle-livedata-2.8.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\fc70e012532b6eb0e385febc5aaf0635\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\fc70e012532b6eb0e385febc5aaf0635\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\57ff1b16d28c33f8d063d90a75f7b537\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\57ff1b16d28c33f8d063d90a75f7b537\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\1709a919c83892f327f230de2091f244\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\1709a919c83892f327f230de2091f244\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\e810deac697feab8d2a0acfdc8f9cef5\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\e810deac697feab8d2a0acfdc8f9cef5\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\4d28bde63148b6b18eb39174d8cf0994\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\4d28bde63148b6b18eb39174d8cf0994\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a3a70dcdbdfd4674961d840abf29097\transformed\core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a3a70dcdbdfd4674961d840abf29097\transformed\core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\fcfbb0aa032cdca161ce213b34c37851\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\fcfbb0aa032cdca161ce213b34c37851\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\8eb842c10b8c11b900558b9b1d222889\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\8eb842c10b8c11b900558b9b1d222889\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\241ac0666bab5c06f579dcdd49058e31\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\241ac0666bab5c06f579dcdd49058e31\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\298b31876c0b07be2665ab2b97c6a6dc\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\298b31876c0b07be2665ab2b97c6a6dc\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b513cf0c6c80823111cf115846e490d5\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b513cf0c6c80823111cf115846e490d5\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\AndroidProject\AliyunCertTool\app\src\main\AndroidManifest.xml
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\75f659e769d841329b7f2d2243cabfd5\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\75f659e769d841329b7f2d2243cabfd5\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\75f659e769d841329b7f2d2243cabfd5\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\b1e63eb5eb0681e2e793bedfd5b2b54c\transformed\ui-test-manifest-1.7.3\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\b1e63eb5eb0681e2e793bedfd5b2b54c\transformed\ui-test-manifest-1.7.3\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.3] C:\Users\<USER>\.gradle\caches\transforms-4\b1e63eb5eb0681e2e793bedfd5b2b54c\transformed\ui-test-manifest-1.7.3\AndroidManifest.xml:24:13-63
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cdf3e867b1382a5d5b2e8e0e042d4c1c\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\8b095931678b4e60ac63c99c8bf47483\transformed\lifecycle-process-2.8.5\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\8b095931678b4e60ac63c99c8bf47483\transformed\lifecycle-process-2.8.5\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\8eb842c10b8c11b900558b9b1d222889\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\8eb842c10b8c11b900558b9b1d222889\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cdf3e867b1382a5d5b2e8e0e042d4c1c\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cdf3e867b1382a5d5b2e8e0e042d4c1c\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cdf3e867b1382a5d5b2e8e0e042d4c1c\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cdf3e867b1382a5d5b2e8e0e042d4c1c\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cdf3e867b1382a5d5b2e8e0e042d4c1c\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cdf3e867b1382a5d5b2e8e0e042d4c1c\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cdf3e867b1382a5d5b2e8e0e042d4c1c\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b5ca4484bc6448589137ef4a4606beed\transformed\window-1.3.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b5ca4484bc6448589137ef4a4606beed\transformed\window-1.3.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b5ca4484bc6448589137ef4a4606beed\transformed\window-1.3.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b5ca4484bc6448589137ef4a4606beed\transformed\window-1.3.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b5ca4484bc6448589137ef4a4606beed\transformed\window-1.3.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b5ca4484bc6448589137ef4a4606beed\transformed\window-1.3.0\AndroidManifest.xml:27:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.Aries.AliyunCertTool.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.Aries.AliyunCertTool.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\70c31411d223bd8dbd73cf3117964047\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\8b095931678b4e60ac63c99c8bf47483\transformed\lifecycle-process-2.8.5\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\8b095931678b4e60ac63c99c8bf47483\transformed\lifecycle-process-2.8.5\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.5] C:\Users\<USER>\.gradle\caches\transforms-4\8b095931678b4e60ac63c99c8bf47483\transformed\lifecycle-process-2.8.5\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2b7d8009e8ab565d3d179232da39eba\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
