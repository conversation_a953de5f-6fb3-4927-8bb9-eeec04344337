{"logs": [{"outputFile": "com.Aries.AliyunCertTool.app-mergeDebugResources-54:/values-uz/values-uz.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9dddf8c9e00e2a90c0a65ed3f1ecb3d3\\transformed\\material3-release\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,287,401,515,612,710,825,958,1067,1209,1293,1397,1491,1589,1703,1824,1933,2058,2181,2311,2479,2604,2725,2849,2970,3065,3163,3280,3406,3510,3620,3727,3850,3978,4091,4195,4279,4375,4469,4599,4687,4773,4874,4954,5038,5138,5242,5338,5437,5525,5633,5733,5836,5975,6055,6171", "endColumns": "116,114,113,113,96,97,114,132,108,141,83,103,93,97,113,120,108,124,122,129,167,124,120,123,120,94,97,116,125,103,109,106,122,127,112,103,83,95,93,129,87,85,100,79,83,99,103,95,98,87,107,99,102,138,79,115,102", "endOffsets": "167,282,396,510,607,705,820,953,1062,1204,1288,1392,1486,1584,1698,1819,1928,2053,2176,2306,2474,2599,2720,2844,2965,3060,3158,3275,3401,3505,3615,3722,3845,3973,4086,4190,4274,4370,4464,4594,4682,4768,4869,4949,5033,5133,5237,5333,5432,5520,5628,5728,5831,5970,6050,6166,6269"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1590,1707,1822,1936,2050,2147,2245,2360,2493,2602,2744,2828,2932,3026,3124,3238,3359,3468,3593,3716,3846,4014,4139,4260,4384,4505,4600,4698,4815,4941,5045,5155,5262,5385,5513,5626,5730,5814,5910,6004,6134,6222,6308,6409,6489,6573,6673,6777,6873,6972,7060,7168,7268,7371,7510,7590,7706", "endColumns": "116,114,113,113,96,97,114,132,108,141,83,103,93,97,113,120,108,124,122,129,167,124,120,123,120,94,97,116,125,103,109,106,122,127,112,103,83,95,93,129,87,85,100,79,83,99,103,95,98,87,107,99,102,138,79,115,102", "endOffsets": "1702,1817,1931,2045,2142,2240,2355,2488,2597,2739,2823,2927,3021,3119,3233,3354,3463,3588,3711,3841,4009,4134,4255,4379,4500,4595,4693,4810,4936,5040,5150,5257,5380,5508,5621,5725,5809,5905,5999,6129,6217,6303,6404,6484,6568,6668,6772,6868,6967,7055,7163,7263,7366,7505,7585,7701,7804"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\70c31411d223bd8dbd73cf3117964047\\transformed\\core-1.13.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,360,460,568,672,791", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "152,254,355,455,563,667,786,887"}, "to": {"startLines": "3,4,5,6,7,8,9,84", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "190,292,394,495,595,703,807,8621", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "287,389,490,590,698,802,921,8717"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\71e603f77f5431c60c0bf20ec43c96c0\\transformed\\material-release\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "88", "endOffsets": "139"}, "to": {"startLines": "74", "startColumns": "4", "startOffsets": "7809", "endColumns": "88", "endOffsets": "7893"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\cf96ea3900c1f15eea4117a61e73d29e\\transformed\\ui-release\\res\\values-uz\\values-uz.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "191,290,375,479,586,682,765,855,948,1031,1112,1195,1269,1354,1430,1505,1578,1661,1729", "endColumns": "98,84,103,106,95,82,89,92,82,80,82,73,84,75,74,72,82,67,116", "endOffsets": "285,370,474,581,677,760,850,943,1026,1107,1190,1264,1349,1425,1500,1573,1656,1724,1841"}, "to": {"startLines": "10,11,12,13,14,15,16,75,76,77,78,79,80,81,82,83,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "926,1025,1110,1214,1321,1417,1500,7898,7991,8074,8155,8238,8312,8397,8473,8548,8722,8805,8873", "endColumns": "98,84,103,106,95,82,89,92,82,80,82,73,84,75,74,72,82,67,116", "endOffsets": "1020,1105,1209,1316,1412,1495,1585,7986,8069,8150,8233,8307,8392,8468,8543,8616,8800,8868,8985"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b59206c725e3c03c5728e46c84e8a9c8\\transformed\\foundation-release\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,140,251", "endColumns": "84,110,100", "endOffsets": "135,246,347"}, "to": {"startLines": "2,88,89", "startColumns": "4,4,4", "startOffsets": "105,8990,9101", "endColumns": "84,110,100", "endOffsets": "185,9096,9197"}}]}]}