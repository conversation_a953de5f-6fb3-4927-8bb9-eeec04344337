{"logs": [{"outputFile": "com.Aries.AliyunCertTool.app-mergeDebugResources-54:/values-zh-rHK/values-zh-rHK.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\cf96ea3900c1f15eea4117a61e73d29e\\transformed\\ui-release\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "177,253,327,415,506,584,658,735,813,887,960,1035,1102,1183,1256,1326,1395,1470,1535", "endColumns": "75,73,87,90,77,73,76,77,73,72,74,66,80,72,69,68,74,64,115", "endOffsets": "248,322,410,501,579,653,730,808,882,955,1030,1097,1178,1251,1321,1390,1465,1530,1646"}, "to": {"startLines": "10,11,12,13,14,15,16,75,76,77,78,79,80,81,82,83,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "837,913,987,1075,1166,1244,1318,7001,7079,7153,7226,7301,7368,7449,7522,7592,7762,7837,7902", "endColumns": "75,73,87,90,77,73,76,77,73,72,74,66,80,72,69,68,74,64,115", "endOffsets": "908,982,1070,1161,1239,1313,1390,7074,7148,7221,7296,7363,7444,7517,7587,7656,7832,7897,8013"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9dddf8c9e00e2a90c0a65ed3f1ecb3d3\\transformed\\material3-release\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,158,260,364,465,556,645,750,855,960,1076,1158,1254,1338,1426,1531,1644,1745,1853,1959,2067,2183,2288,2390,2495,2601,2686,2781,2886,2995,3085,3187,3285,3394,3508,3608,3699,3772,3862,3951,4042,4125,4207,4296,4376,4458,4555,4649,4742,4835,4919,5015,5111,5206,5314,5394,5486", "endColumns": "102,101,103,100,90,88,104,104,104,115,81,95,83,87,104,112,100,107,105,107,115,104,101,104,105,84,94,104,108,89,101,97,108,113,99,90,72,89,88,90,82,81,88,79,81,96,93,92,92,83,95,95,94,107,79,91,89", "endOffsets": "153,255,359,460,551,640,745,850,955,1071,1153,1249,1333,1421,1526,1639,1740,1848,1954,2062,2178,2283,2385,2490,2596,2681,2776,2881,2990,3080,3182,3280,3389,3503,3603,3694,3767,3857,3946,4037,4120,4202,4291,4371,4453,4550,4644,4737,4830,4914,5010,5106,5201,5309,5389,5481,5571"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1395,1498,1600,1704,1805,1896,1985,2090,2195,2300,2416,2498,2594,2678,2766,2871,2984,3085,3193,3299,3407,3523,3628,3730,3835,3941,4026,4121,4226,4335,4425,4527,4625,4734,4848,4948,5039,5112,5202,5291,5382,5465,5547,5636,5716,5798,5895,5989,6082,6175,6259,6355,6451,6546,6654,6734,6826", "endColumns": "102,101,103,100,90,88,104,104,104,115,81,95,83,87,104,112,100,107,105,107,115,104,101,104,105,84,94,104,108,89,101,97,108,113,99,90,72,89,88,90,82,81,88,79,81,96,93,92,92,83,95,95,94,107,79,91,89", "endOffsets": "1493,1595,1699,1800,1891,1980,2085,2190,2295,2411,2493,2589,2673,2761,2866,2979,3080,3188,3294,3402,3518,3623,3725,3830,3936,4021,4116,4221,4330,4420,4522,4620,4729,4843,4943,5034,5107,5197,5286,5377,5460,5542,5631,5711,5793,5890,5984,6077,6170,6254,6350,6446,6541,6649,6729,6821,6911"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\71e603f77f5431c60c0bf20ec43c96c0\\transformed\\material-release\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "84", "endOffsets": "135"}, "to": {"startLines": "74", "startColumns": "4", "startOffsets": "6916", "endColumns": "84", "endOffsets": "6996"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\70c31411d223bd8dbd73cf3117964047\\transformed\\core-1.13.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "3,4,5,6,7,8,9,84", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "176,268,367,461,555,648,741,7661", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "263,362,456,550,643,736,832,7757"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b59206c725e3c03c5728e46c84e8a9c8\\transformed\\foundation-release\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,126,207", "endColumns": "70,80,76", "endOffsets": "121,202,279"}, "to": {"startLines": "2,88,89", "startColumns": "4,4,4", "startOffsets": "105,8018,8099", "endColumns": "70,80,76", "endOffsets": "171,8094,8171"}}]}]}