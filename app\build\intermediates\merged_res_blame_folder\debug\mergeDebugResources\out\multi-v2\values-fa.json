{"logs": [{"outputFile": "com.Aries.AliyunCertTool.app-mergeDebugResources-54:/values-fa/values-fa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\70c31411d223bd8dbd73cf3117964047\\transformed\\core-1.13.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,355,455,556,662,779", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "149,251,350,450,551,657,774,875"}, "to": {"startLines": "3,4,5,6,7,8,9,84", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "184,283,385,484,584,685,791,8494", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "278,380,479,579,680,786,903,8590"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b59206c725e3c03c5728e46c84e8a9c8\\transformed\\foundation-release\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,134,223", "endColumns": "78,88,88", "endOffsets": "129,218,307"}, "to": {"startLines": "2,88,89", "startColumns": "4,4,4", "startOffsets": "105,8854,8943", "endColumns": "78,88,88", "endOffsets": "179,8938,9027"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9dddf8c9e00e2a90c0a65ed3f1ecb3d3\\transformed\\material3-release\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,280,394,512,608,704,818,957,1073,1208,1293,1396,1488,1585,1699,1822,1930,2063,2194,2316,2481,2603,2716,2832,2949,3042,3140,3261,3393,3500,3603,3708,3839,3975,4081,4191,4271,4364,4461,4582,4668,4752,4851,4933,5017,5118,5219,5316,5416,5503,5607,5707,5810,5930,6012,6116", "endColumns": "114,109,113,117,95,95,113,138,115,134,84,102,91,96,113,122,107,132,130,121,164,121,112,115,116,92,97,120,131,106,102,104,130,135,105,109,79,92,96,120,85,83,98,81,83,100,100,96,99,86,103,99,102,119,81,103,97", "endOffsets": "165,275,389,507,603,699,813,952,1068,1203,1288,1391,1483,1580,1694,1817,1925,2058,2189,2311,2476,2598,2711,2827,2944,3037,3135,3256,3388,3495,3598,3703,3834,3970,4076,4186,4266,4359,4456,4577,4663,4747,4846,4928,5012,5113,5214,5311,5411,5498,5602,5702,5805,5925,6007,6111,6209"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1537,1652,1762,1876,1994,2090,2186,2300,2439,2555,2690,2775,2878,2970,3067,3181,3304,3412,3545,3676,3798,3963,4085,4198,4314,4431,4524,4622,4743,4875,4982,5085,5190,5321,5457,5563,5673,5753,5846,5943,6064,6150,6234,6333,6415,6499,6600,6701,6798,6898,6985,7089,7189,7292,7412,7494,7598", "endColumns": "114,109,113,117,95,95,113,138,115,134,84,102,91,96,113,122,107,132,130,121,164,121,112,115,116,92,97,120,131,106,102,104,130,135,105,109,79,92,96,120,85,83,98,81,83,100,100,96,99,86,103,99,102,119,81,103,97", "endOffsets": "1647,1757,1871,1989,2085,2181,2295,2434,2550,2685,2770,2873,2965,3062,3176,3299,3407,3540,3671,3793,3958,4080,4193,4309,4426,4519,4617,4738,4870,4977,5080,5185,5316,5452,5558,5668,5748,5841,5938,6059,6145,6229,6328,6410,6494,6595,6696,6793,6893,6980,7084,7184,7287,7407,7489,7593,7691"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\71e603f77f5431c60c0bf20ec43c96c0\\transformed\\material-release\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "87", "endOffsets": "138"}, "to": {"startLines": "74", "startColumns": "4", "startOffsets": "7696", "endColumns": "87", "endOffsets": "7779"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\cf96ea3900c1f15eea4117a61e73d29e\\transformed\\ui-release\\res\\values-fa\\values-fa.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "185,272,351,445,543,629,711,814,899,982,1063,1145,1219,1303,1378,1452,1524,1599,1666", "endColumns": "86,78,93,97,85,81,102,84,82,80,81,73,83,74,73,71,74,66,116", "endOffsets": "267,346,440,538,624,706,809,894,977,1058,1140,1214,1298,1373,1447,1519,1594,1661,1778"}, "to": {"startLines": "10,11,12,13,14,15,16,75,76,77,78,79,80,81,82,83,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "908,995,1074,1168,1266,1352,1434,7784,7869,7952,8033,8115,8189,8273,8348,8422,8595,8670,8737", "endColumns": "86,78,93,97,85,81,102,84,82,80,81,73,83,74,73,71,74,66,116", "endOffsets": "990,1069,1163,1261,1347,1429,1532,7864,7947,8028,8110,8184,8268,8343,8417,8489,8665,8732,8849"}}]}]}