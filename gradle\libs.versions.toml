[versions]
agp = "8.5.0"
kotlin = "1.9.0"
coreKtx = "1.13.0"
junit = "4.13.2"
junitVersion = "1.2.1"
espressoCore = "3.6.1"
lifecycleRuntimeKtx = "2.6.1"
activityCompose = "1.9.2"
composeBom = "2024.09.03"
lifecycleViewmodelCompose = "2.8.5"
alb-sdk = "1.2.8"
cas-sdk = "3.0.1"

[libraries]
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
junit = { group = "junit", name = "junit", version.ref = "junit" }
androidx-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
androidx-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
androidx-lifecycle-runtime-ktx = { group = "androidx.lifecycle", name = "lifecycle-runtime-ktx", version.ref = "lifecycleRuntimeKtx" }
androidx-activity-compose = { group = "androidx.activity", name = "activity-compose", version.ref = "activityCompose" }
androidx-compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "composeBom" }
androidx-ui = { group = "androidx.compose.ui", name = "ui" }
androidx-ui-graphics = { group = "androidx.compose.ui", name = "ui-graphics" }
androidx-ui-tooling = { group = "androidx.compose.ui", name = "ui-tooling" }
androidx-ui-tooling-preview = { group = "androidx.compose.ui", name = "ui-tooling-preview" }
androidx-ui-test-manifest = { group = "androidx.compose.ui", name = "ui-test-manifest" }
androidx-ui-test-junit4 = { group = "androidx.compose.ui", name = "ui-test-junit4" }
androidx-material3 = { group = "androidx.compose.material3", name = "material3" }
androidx-material-icons = { group = "androidx.compose.material", name = "material-icons-extended"}
# ========== Compose 扩展依赖项 ==========
# Foundation - Compose 基础组件库 (布局、手势、绘制、动画)
androidx-compose-foundation = { group = "androidx.compose.foundation", name = "foundation" }
# Material Design 2 - 上一代设计语言组件库
androidx-compose-material = { group = "androidx.compose.material", name = "material" }
# Material 图标核心库 - 基础图标集
androidx-material-icons-core = { group = "androidx.compose.material", name = "material-icons-core" }
# Material3 自适应组件 - 响应式设计工具
androidx-material3-adaptive = { group = "androidx.compose.material3.adaptive", name = "adaptive" }
# ViewModel 与 Compose 集成 - 状态管理
androidx-lifecycle-viewmodel-compose = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-compose", version.ref = "lifecycleViewmodelCompose" }
# LiveData 与 Compose 集成 - 数据观察
androidx-compose-runtime-livedata = { group = "androidx.compose.runtime", name = "runtime-livedata" }
# RxJava 与 Compose 集成 - 响应式编程
androidx-compose-runtime-rxjava2 = { group = "androidx.compose.runtime", name = "runtime-rxjava2" }
aliyun-alb = { group = "com.aliyun", name = "alb20200616" ,version.ref = "alb-sdk" }
aliyun-cas = { group = "com.aliyun", name = "cas20200407" ,version.ref = "cas-sdk" }

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
jetbrains-kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }

