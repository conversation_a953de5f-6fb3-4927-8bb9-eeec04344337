{"logs": [{"outputFile": "com.Aries.AliyunCertTool.app-mergeDebugResources-54:/values-be/values-be.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9dddf8c9e00e2a90c0a65ed3f1ecb3d3\\transformed\\material3-release\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,287,406,523,621,718,832,955,1070,1215,1299,1410,1503,1600,1714,1837,1953,2100,2246,2384,2561,2693,2818,2947,3069,3163,3261,3387,3520,3619,3730,3839,3989,4142,4250,4350,4435,4530,4626,4744,4830,4917,5017,5104,5191,5291,5397,5493,5591,5680,5788,5884,5984,6130,6220,6338", "endColumns": "116,114,118,116,97,96,113,122,114,144,83,110,92,96,113,122,115,146,145,137,176,131,124,128,121,93,97,125,132,98,110,108,149,152,107,99,84,94,95,117,85,86,99,86,86,99,105,95,97,88,107,95,99,145,89,117,95", "endOffsets": "167,282,401,518,616,713,827,950,1065,1210,1294,1405,1498,1595,1709,1832,1948,2095,2241,2379,2556,2688,2813,2942,3064,3158,3256,3382,3515,3614,3725,3834,3984,4137,4245,4345,4430,4525,4621,4739,4825,4912,5012,5099,5186,5286,5392,5488,5586,5675,5783,5879,5979,6125,6215,6333,6429"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1546,1663,1778,1897,2014,2112,2209,2323,2446,2561,2706,2790,2901,2994,3091,3205,3328,3444,3591,3737,3875,4052,4184,4309,4438,4560,4654,4752,4878,5011,5110,5221,5330,5480,5633,5741,5841,5926,6021,6117,6235,6321,6408,6508,6595,6682,6782,6888,6984,7082,7171,7279,7375,7475,7621,7711,7829", "endColumns": "116,114,118,116,97,96,113,122,114,144,83,110,92,96,113,122,115,146,145,137,176,131,124,128,121,93,97,125,132,98,110,108,149,152,107,99,84,94,95,117,85,86,99,86,86,99,105,95,97,88,107,95,99,145,89,117,95", "endOffsets": "1658,1773,1892,2009,2107,2204,2318,2441,2556,2701,2785,2896,2989,3086,3200,3323,3439,3586,3732,3870,4047,4179,4304,4433,4555,4649,4747,4873,5006,5105,5216,5325,5475,5628,5736,5836,5921,6016,6112,6230,6316,6403,6503,6590,6677,6777,6883,6979,7077,7166,7274,7370,7470,7616,7706,7824,7920"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\cf96ea3900c1f15eea4117a61e73d29e\\transformed\\ui-release\\res\\values-be\\values-be.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "187,280,364,458,561,647,727,816,904,986,1069,1156,1228,1315,1399,1477,1553,1638,1708", "endColumns": "92,83,93,102,85,79,88,87,81,82,86,71,86,83,77,75,84,69,122", "endOffsets": "275,359,453,556,642,722,811,899,981,1064,1151,1223,1310,1394,1472,1548,1633,1703,1826"}, "to": {"startLines": "10,11,12,13,14,15,16,75,76,77,78,79,80,81,82,83,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "917,1010,1094,1188,1291,1377,1457,8016,8104,8186,8269,8356,8428,8515,8599,8677,8854,8939,9009", "endColumns": "92,83,93,102,85,79,88,87,81,82,86,71,86,83,77,75,84,69,122", "endOffsets": "1005,1089,1183,1286,1372,1452,1541,8099,8181,8264,8351,8423,8510,8594,8672,8748,8934,9004,9127"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b59206c725e3c03c5728e46c84e8a9c8\\transformed\\foundation-release\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,136,223", "endColumns": "80,86,102", "endOffsets": "131,218,321"}, "to": {"startLines": "2,88,89", "startColumns": "4,4,4", "startOffsets": "105,9132,9219", "endColumns": "80,86,102", "endOffsets": "181,9214,9317"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\70c31411d223bd8dbd73cf3117964047\\transformed\\core-1.13.1\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,562,665,786", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "148,250,350,451,557,660,781,882"}, "to": {"startLines": "3,4,5,6,7,8,9,84", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "186,284,386,486,587,693,796,8753", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "279,381,481,582,688,791,912,8849"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\71e603f77f5431c60c0bf20ec43c96c0\\transformed\\material-release\\res\\values-be\\values-be.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "90", "endOffsets": "141"}, "to": {"startLines": "74", "startColumns": "4", "startOffsets": "7925", "endColumns": "90", "endOffsets": "8011"}}]}]}